<x-block :$block>
    <div
        @class([
            'container',
            $bgColor === 'light-grey' ? 'bg-light-gray' : '',
            $bgColor === 'gradient' ? 'bg-gradient-navy-to-light-blue text-white' : '',
            $bgColor === 'grey' ? 'bg-gray' : '',
        ])
    >
        <div
            @class(['relative flex flex-col gap-x-8 gap-y-14 landscape:min-h-[713px]'])
        >
            <x-block-header
                :header="$header"
                :background="$bgColor === 'gradient' ? 'bg-navy' : ''"
            >
                {!! $children !!}
            </x-block-header>

            @if (! empty($image['url']))
                <figure class="w-full rounded bite">
                    <x-image
                        :image="$image"
                        class="max-h-[400px] min-h-[300px] w-full object-cover"
                    />
                </figure>
            @endif
        </div>
    </div>
</x-block>

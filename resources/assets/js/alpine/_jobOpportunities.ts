export default function jobOpportunities(perPageOptions: number[]) {
  return {
    selectedFilters: {
      sort: ''
    },
    perPage: perPageOptions[1],
    searchQuery: '',
    provinces: [],
    taxonomyData: [],
    page: [],
    total_count: 0,
    jobs: [],
    filteredJobs: [],

    get totalPages() {
      return Math.ceil(this.total_count / this.perPage);
    },

    scrollToTop() {
      const element = document.querySelector('.job-opportunities');
      if (element) {
        window.scrollTo({
          top: element.offsetTop,
          behavior: 'smooth'
        });
      }
    },

    nextPage() {
      if (this.page < this.totalPages) {
        this.page++;
        this.applyFilters(false);
        this.scrollToTop();
      }
    },

    prevPage() {
      if (this.page > 1) {
        this.page--;
        this.applyFilters(false);
        this.scrollToTop();
      }
    },

    goToPage(p) {
      if (typeof p === 'number' && p >= 1 && p <= this.totalPages) {
        this.page = p;
        this.applyFilters(false);
        this.scrollToTop();
      }
    },

    pagesToShow() {
      const pages = [];
      const totalPages = this.totalPages;
      const currentPage = this.page;
      const maxPagesToShow = 3;

      if (totalPages <= maxPagesToShow) {
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        if (currentPage > 1) {
          pages.push(currentPage - 1);
        }

        pages.push(currentPage);

        if (currentPage < totalPages) {
          pages.push(currentPage + 1);
        }

        if (currentPage + 1 < totalPages - 1) {
          pages.push('...');
        }

        if (currentPage < totalPages) {
          pages.push(totalPages);
        }
      }

      return pages;
    },

    clearFilters() {
      this.selectedFilters = {};
      this.searchQuery = '';
      this.perPage = perPageOptions[2];
      this.applyFilters();
      this.updateUrl();
    },

    updateUrl() {
      let url = new URL(window.location.origin + window.location.pathname);
      let params = new URLSearchParams();

      Object.keys(this.selectedFilters).forEach((key) => {
        if (this.selectedFilters[key]) {
          params.set(key, this.selectedFilters[key]);
        }
      });

      if (this.searchQuery) params.set('search', this.searchQuery);

      let queryString = params.toString();
      let newUrl = queryString ? `${url}?${queryString}` : url;

      window.history.pushState({}, '', newUrl);
    },

    async fetchAllResources(
      searchQuery = '',
      filters = {},
      page = 1,
      perPage = 12
    ) {
      const params = new URLSearchParams();

      if (searchQuery) {
        params.set('search', searchQuery);
      }

      Object.keys(filters).forEach((key) => {
        if (key === 'location') {
          params.set('province', filters[key]);
        } else if (filters[key]) {
          params.set(key, filters[key]);
        }
      });

      params.set('page', page.toString());
      params.set('perPage', perPage.toString());

      const response = await fetch(
        `/api/takt/v1/job-listing?${params.toString()}`
      );
      const data = await response.json();
      return data;
    },

    applyFilters(resetPage = true) {
      if (!Array.isArray(this.jobs)) return;

      let filters = this.selectedFilters;
      let searchQuery = this.searchQuery ? this.searchQuery.toLowerCase() : '';

      this.fetchAllResources(
        searchQuery,
        filters,
        this.page,
        this.perPage
      ).then((data) => {
        this.filteredJobs = data.jobs;
        this.total_count = data.total_count;

        if (resetPage) {
          this.page = 1;
        }
        this.updateUrl();
      });
    },

    async init() {
      try {
        let urlParams = new URLSearchParams(window.location.search);
        urlParams.forEach((value, key) => {
          if (key === 'search') {
            this.searchQuery = value;
          } else if (key === 'page') {
            this.page = parseInt(value, 10);
          } else {
            this.selectedFilters[key] = value;
          }
        });

        const data = await this.fetchAllResources();
        this.jobs = data.jobs;

        this.filteredJobs = [...this.jobs];
        this.total_count = data.total_count;

        const [provinceResponse] = await Promise.all([
          fetch('/wp-json/wp/v2/province_taxonomy?per_page=100')
        ]);

        const provinces = await provinceResponse.json();

        this.taxonomyData = [...provinces].map((item) => ({
          id: item.id,
          title: item.name || item.title,
          slug: item.slug,
          count: item.count || 0
        }));

        this.applyFilters();
      } catch (error) {
        console.error('Error fetching provinces:', error);
      }
    },

    getTitleByTaxonomyId(termId) {
      return (
        this.taxonomyData.find((item) => item.id === termId)?.title ||
        this.taxonomyData.find((item) => item.id === termId)?.name
      );
    }
  };
}

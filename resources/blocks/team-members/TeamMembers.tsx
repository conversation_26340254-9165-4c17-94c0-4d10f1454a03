import { __ } from '@wordpress/i18n';

import {
  useBlockProps,
  useInnerBlocksProps,
  InspectorControls
} from '@wordpress/block-editor';
import {
  PanelBody,
  __experimentalToggleGroupControl as ToggleGroupControl,
  __experimentalToggleGroupControlOption as ToggleGroupControlOption
} from '@wordpress/components';

import BlockHeader from '../components/BlockHeader';
import { _class } from '../components/_utils';
import TaxonomyMultiSelect from '../components/TaxonomyMultiSelect';

export default function Edit({ attributes, setAttributes }) {
  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    {
      className: 'grid grid-cols-3 gap-y-16 gap-x-8'
    },
    {
      allowedBlocks: ['takt/team-members-item'],
      template: [
        ['takt/team-members-item'],
        ['takt/team-members-item'],
        ['takt/team-members-item']
      ]
    }
  );

  return (
    <>
      <InspectorControls>
        <PanelBody title={__('Settings', 'takt')}>
          <ToggleGroupControl
            label={__('Method', 'takt')}
            value={attributes.method}
            onChange={(value) => setAttributes({ method: value })}
          >
            <ToggleGroupControlOption value="auto" label={__('Auto', 'takt')} />
            <ToggleGroupControlOption
              value="manual"
              label={__('Manual', 'takt')}
            />
          </ToggleGroupControl>
          {attributes.method === 'auto' && (
            <TaxonomyMultiSelect
              label="Exclude"
              value={attributes.exclude}
              taxonomy="position"
              onChange={(value) => setAttributes({ exclude: value })}
            />
          )}
        </PanelBody>
      </InspectorControls>
      <div
        {...useBlockProps({
          className: 'container relative flex flex-col gap-y-16 px-16 py-16'
        })}
      >
        <div className="flex flex-col gap-y-16">
          <BlockHeader
            setAttributes={setAttributes}
            header={attributes.header}
          />
          {attributes.method === 'manual' ? (
            <div {...innerBlocksProps}>{children}</div>
          ) : (
            <div className="relative inset-0 z-10 flex flex-col items-center justify-center border-t pb-48 pt-0">
              {/* <div
                className="absolute inset-0 bg-contain bg-center bg-no-repeat opacity-10"
                style={{ backgroundImage: `url(${PreviewImage})` }}
              ></div> */}

              <div className="pt-32 text-center">
                <h1 className="font-heading text-5xl">Team Members</h1>
                <p className="text-gray-600 mt-4 text-lg">
                  {__('Team Members are displayed on the front-end.', 'takt')}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}

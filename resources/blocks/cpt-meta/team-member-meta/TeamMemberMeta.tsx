import { __ } from '@wordpress/i18n';

import {
  useBlockProps,
  RichText,
  useInnerBlocksProps
} from '@wordpress/block-editor';
import { useEntityProp, store as coreStore } from '@wordpress/core-data';
import { useSelect } from '@wordpress/data';

import { _class } from '../../components/_utils';
import { Content_Blocks, PlaceholderImage } from '../../components';

export default function Edit({ attributes, setAttributes, context }) {
  const [imageId, updateImageId] = useEntityProp(
    'postType',
    'team-member',
    'featured_media',
    context.postId
  );
  const image = useSelect(
    (select) => select(coreStore).getMedia(imageId),
    [imageId]
  );

  const [meta, updateMeta] = useEntityProp(
    'postType',
    'team-member',
    'meta',
    context.postId
  );

  const [title, updateTitle] = useEntityProp(
    'postType',
    'team-member',
    'title',
    context.postId
  );

  const [excerpt, updateExcerpt] = useEntityProp(
    'postType',
    'team-member',
    'excerpt',
    context.postId
  );

  const { email, location, linkedin, phone } = meta;

  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    {
      className: 'prose'
    },
    {
      allowedBlocks: [...Content_Blocks],
      templateLock: false
    }
  );

  return (
    <div>
      <div
        {...useBlockProps({
          className: 'max-w-4xl mx-auto bg-lightGray'
        })}
      >
        <div className="grid grid-cols-3 gap-x-6 p-6">
          <div className="space-y-5">
            <div>
              <PlaceholderImage
                image={{
                  id: image?.id,
                  url: image?.source_url
                }}
                className="aspect-square w-full"
                imageClass="size-full object-cover"
                placeholderClass="size-full"
                onSelect={(image) => {
                  updateImageId(image.id);
                }}
                onDelete={() => {
                  updateImageId(null);
                }}
              />
            </div>
            <div>
              <RichText
                tagName="div"
                className={`text-xl font-bold capitalize`}
                placeholder={__('Full Name', 'takt')}
                value={title}
                allowedFormats={[]}
                onChange={(value) => {
                  updateTitle(value);
                }}
                keepPlaceholderOnFocus
                disableLineBreaks={true}
              />
            </div>
            <RichText
              tagName="div"
              className={`text-base`}
              placeholder={__('Location', 'takt')}
              value={location}
              allowedFormats={[]}
              onChange={(value) => {
                updateMeta({ location: value });
              }}
              keepPlaceholderOnFocus
              disableLineBreaks={true}
            />
            <RichText
              tagName="div"
              className={`text-sm`}
              placeholder={__('Short Bio...', 'takt')}
              value={excerpt}
              allowedFormats={[]}
              onChange={(value) => {
                updateExcerpt(value);
              }}
              keepPlaceholderOnFocus
              disableLineBreaks={true}
            />
            <div>
              <div className="flex items-center gap-4">
                <div>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M4 4.00049H20C21.1046 4.00049 22 4.89592 22 6.00049V18.0005C22 19.1051 21.1046 20.0005 20 20.0005H4C2.89543 20.0005 2 19.1051 2 18.0005V6.00049C2 4.89592 2.89543 4.00049 4 4.00049ZM13.65 15.4505L20 11.0005V8.90049L12.65 14.0505C12.2591 14.3218 11.7409 14.3218 11.35 14.0505L4 8.90049V11.0005L10.35 15.4505C11.341 16.1432 12.659 16.1432 13.65 15.4505Z"
                      className="fill-navy"
                    />
                  </svg>
                </div>
                <RichText
                  tagName="div"
                  className={`text-xs`}
                  placeholder={__('Email', 'takt')}
                  value={email}
                  allowedFormats={[]}
                  onChange={(value) => {
                    updateMeta({ email: value });
                  }}
                  keepPlaceholderOnFocus
                  disableLineBreaks={true}
                />
              </div>
              <div className="flex items-center gap-4">
                <div>
                  <svg
                    width="24"
                    height="25"
                    viewBox="0 0 24 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      id="Vector"
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M4.5 4.03174C3.67157 4.03174 3 4.70331 3 5.53174V20.5317C3 21.3601 3.67157 22.0317 4.5 22.0317H19.5C20.3284 22.0317 21 21.3601 21 20.5317V5.53174C21 4.70331 20.3284 4.03174 19.5 4.03174H4.5ZM8.52076 8.03446C8.52639 8.99071 7.81061 9.57993 6.96123 9.57571C6.16107 9.57149 5.46357 8.93446 5.46779 8.03587C5.47201 7.19071 6.13998 6.51149 7.00764 6.53118C7.88795 6.55087 8.52639 7.19634 8.52076 8.03446ZM12.2797 10.7935H9.75971H9.7583V19.3533H12.4217V19.1536C12.4217 18.7737 12.4214 18.3937 12.4211 18.0136C12.4203 16.9998 12.4194 15.9849 12.4246 14.9714C12.426 14.7253 12.4372 14.4694 12.5005 14.2345C12.7381 13.357 13.5271 12.7903 14.4074 12.9296C14.9727 13.0181 15.3467 13.3458 15.5042 13.8788C15.6013 14.212 15.6449 14.5706 15.6491 14.918C15.6605 15.9656 15.6589 17.0132 15.6573 18.0609C15.6567 18.4307 15.6561 18.8007 15.6561 19.1705V19.3519H18.328V19.1466C18.328 18.6946 18.3278 18.2427 18.3275 17.7908C18.327 16.6613 18.3264 15.5318 18.3294 14.4019C18.3308 13.8914 18.276 13.388 18.1508 12.8944C17.9638 12.1603 17.5771 11.5528 16.9485 11.1141C16.5027 10.8019 16.0133 10.6008 15.4663 10.5783C15.404 10.5757 15.3412 10.5724 15.2781 10.5689C14.9984 10.5538 14.7141 10.5385 14.4467 10.5924C13.6817 10.7457 13.0096 11.0958 12.5019 11.7131C12.4429 11.7839 12.3852 11.8558 12.2991 11.9631L12.2797 11.9874V10.7935ZM5.68164 19.3561H8.33242V10.7991H5.68164V19.3561Z"
                      className="fill-navy"
                    />
                  </svg>
                </div>
                <RichText
                  tagName="div"
                  className={`text-xs`}
                  placeholder={__('LinkedIn', 'takt')}
                  value={linkedin}
                  allowedFormats={[]}
                  onChange={(value) => {
                    updateMeta({ linkedin: value });
                  }}
                  keepPlaceholderOnFocus
                  disableLineBreaks={true}
                />
              </div>
              <div className="flex items-center gap-4">
                <div>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <path
                      d="M17 21.0003C15.3463 20.9988 13.7183 20.5901 12.26 19.8103L11.81 19.5603C8.70075 17.8886 6.15169 15.3395 4.48 12.2303L4.23 11.7803C3.42982 10.3137 3.00713 8.67097 3 7.00027V6.33027C2.99958 5.79723 3.21196 5.28607 3.59 4.91027L5.28 3.22027C5.44413 3.05487 5.67581 2.97515 5.90696 3.00453C6.13811 3.03391 6.34248 3.16907 6.46 3.37027L8.71 7.23027C8.93753 7.62316 8.87183 8.12003 8.55 8.44027L6.66 10.3303C6.50304 10.4855 6.46647 10.7253 6.57 10.9203L6.92 11.5803C8.17704 13.9087 10.0893 15.8175 12.42 17.0703L13.08 17.4303C13.275 17.5338 13.5148 17.4972 13.67 17.3403L15.56 15.4503C15.8802 15.1285 16.3771 15.0628 16.77 15.2903L20.63 17.5403C20.8312 17.6578 20.9664 17.8622 20.9957 18.0933C21.0251 18.3245 20.9454 18.5562 20.78 18.7203L19.09 20.4103C18.7142 20.7883 18.203 21.0007 17.67 21.0003H17Z"
                      className="fill-navy"
                    />
                  </svg>
                </div>
                <RichText
                  tagName="div"
                  className={`text-xs`}
                  placeholder={__('Phone Number', 'takt')}
                  value={phone}
                  allowedFormats={[]}
                  onChange={(value) => {
                    updateMeta({ phone: value });
                  }}
                  keepPlaceholderOnFocus
                  disableLineBreaks={true}
                />
              </div>
            </div>
          </div>
          <div className="col-span-2 px-12">
            <div className="h-0 min-h-full overflow-auto">
              <div {...innerBlocksProps}>{children}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

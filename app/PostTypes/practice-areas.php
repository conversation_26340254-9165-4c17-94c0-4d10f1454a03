<?php

namespace App\PostTypes;

/**
 * Create Custom Post Type for Practice Areas
 */
add_action(
    'init',
    function () {
        $customArgs = [
            'menu_position' => 9,
            'supports' => [
                'editor',
                'title',
                'thumbnail',
                'revisions',
                'custom-fields'
            ],
            'has_archive' => false,
            'public' => true,
            'publicly_queryable' => false,
            'show_in_rest' => true,
            'rewrite' => [
                'slug' => 'practice-area',
                'with_front' => false,
            ],
            'show_in_nav_menus' => true,
            'menu_icon' =>
            'data:image/svg+xml;base64,' .
                base64_encode(
                    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6"><path fill-rule="evenodd" d="M2.25 4.125c0-1.036.84-1.875 1.875-1.875h5.25c1.036 0 1.875.84 1.875 1.875V17.25a4.5 4.5 0 1 1-9 0V4.125Zm4.5 14.25a1.125 1.125 0 1 0 0-2.25 1.125 1.125 0 0 0 0 2.25Z" clip-rule="evenodd" /><path d="M10.719 21.75h9.156c1.036 0 1.875-.84 1.875-1.875v-5.25c0-1.036-.84-1.875-1.875-1.875h-.14l-8.742 8.743c-.09.089-.18.175-.274.257ZM12.738 17.625l6.474-6.474a1.875 1.875 0 0 0 0-2.651L15.5 4.787a1.875 1.875 0 0 0-2.651 0l-.1.099V17.25c0 .126-.003.251-.01.375Z" /></svg>'
                ),
            'template_lock' => 'insert',
            'template' => [
                [
                    'takt/practice-area-meta',
                    [
                        'lock' => [
                            'remove' => true,
                            'move' => true,
                        ],
                    ],
                ],
            ],
        ];

        \new_custom_post_type('practice-area', 'Practice Area', 'Practice Areas', $customArgs, 0);

        $postMeta = [
            'description',
            'subtitle',
            'content',
        ];

        foreach ($postMeta as $key => $customArgs) {
            $args = [
                'show_in_rest' => true,
                'single' => true,
                'type' => 'string',
                'sanitize_callback' => 'wp_kses_post',
            ];

            if (is_numeric($key)) {
                $key = $customArgs;
            }

            if (is_array($customArgs)) {
                $args = array_merge($args, $customArgs);
            }

            register_post_meta('practice-area', $key, $args);
        }
    },
    0
);

add_filter('practice', function ($title) {
    $screen = get_current_screen();

    if ($screen->post_type == 'practice-area') {
        $title = 'Practice Areas';
    }

    return $title;
});

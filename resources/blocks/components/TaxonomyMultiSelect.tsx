import {
  CheckboxControl,
  __experimentalScrollable as Scrollable,
  TextControl,
  __experimentalVStack as VStack
} from '@wordpress/components';
import { useEntityProp, useEntityRecords } from '@wordpress/core-data';
import { useMemo, useState } from '@wordpress/element';
import { decodeEntities } from '@wordpress/html-entities';

import { sortBySelected } from './_utils';

type TaxonomyMultiSelectProps = {
  label?: string;
  value: number[];
  taxonomy?: string;
  onChange?: (itemIds: number[]) => void;
};

const TaxonomyMultiSelect: React.FC<TaxonomyMultiSelectProps> = ({
  label = 'Select Posts',
  value = [],
  taxonomy = 'category',
  onChange = () => {}
}) => {
  const [filterValue, setFilterValue] = useState('');
  const [filteredItems, setFilteredItems] = useState([]);

  const { hasResolved, records } = useEntityRecords('taxonomy', taxonomy, {
    per_page: -1
  });

  const availableItems = useMemo(() => {
    return hasResolved ? sortBySelected(records, value ?? []) : [];
  }, [hasResolved, value]);

  const renderItems = (renderedItems) => {
    return renderedItems.map((item) => {
      return (
        <div key={item.id}>
          <CheckboxControl
            __nextHasNoMarginBottom
            checked={value.indexOf(item.id) !== -1}
            onChange={() => {
              const itemId = parseInt(item.id, 10);
              const hasItem = value.includes(itemId);

              const _items = hasItem
                ? value.filter((id) => id !== itemId)
                : [...value, itemId];

              onChange(_items);
            }}
            label={decodeEntities(item.name)}
          />
        </div>
      );
    });
  };

  const setFilter = (value) => {
    setFilterValue(value);
    setFilteredItems(
      availableItems.filter((item) =>
        item.name.toLowerCase().startsWith(value.toLowerCase())
      )
    );
  };

  return (
    <div>
      {hasResolved && value && (
        <>
          <TextControl
            __nextHasNoMarginBottom
            label={label}
            value={filterValue}
            onChange={setFilter}
            autoComplete="off"
          />
          <Scrollable
            className="rounded rounded-t-none border border-t-0 p-2"
            style={{ maxHeight: 100 }}
          >
            <VStack spacing={1}>
              {renderItems(filterValue !== '' ? filteredItems : availableItems)}
            </VStack>
          </Scrollable>
        </>
      )}
    </div>
  );
};

export default TaxonomyMultiSelect;

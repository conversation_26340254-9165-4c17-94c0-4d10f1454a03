@php
    $eyebrow = $block->attributes['eyebrow'] ?? '';
    $location = $block->attributes['location'] ?? '';
    $address = $block->attributes['address'] ?? '';
    $phone1 = $block->attributes['phone1'] ?? '';
    $phone2 = $block->attributes['phone2'] ?? '';
    $email = $block->attributes['email'] ?? '';
@endphp

<div class="bg-white p-8 bite bite-x-1.5 bite-y-1 bite-5">
    <div class="flex flex-col-reverse gap-2">
        <h4 class="text-xl font-bold leading-9 text-navy">
            {!! $location !!}
        </h4>
        <span
            class="text-sm font-bold uppercase leading-none tracking-ultra text-black"
        >
            {!! $eyebrow !!}
        </span>
    </div>
    <address class="mt-3 text-sm not-italic md:mt-4">
        {!! $address !!}
    </address>

    <div class="mt-3 space-y-4 md:mt-4">
        @unless (empty($email))
            <a href="mailto:{!! $email !!}" class="group flex gap-2">
                <x-icons.mail
                    class="size-6 text-navy transition-colors group-hover:text-teal"
                />
                <span
                    class="sr-only text-wrap break-all text-sm underline decoration-transparent transition-colors group-hover:decoration-teal"
                >
                    {!! $email !!}
                </span>
            </a>
        @endunless

        @unless (empty($phone1))
            <a href="tel:{!! $phone1 !!}" class="group flex gap-2">
                <x-icons.phone
                    class="size-6 text-navy transition-colors group-hover:text-teal"
                />
                <span
                    class="text-sm underline decoration-transparent transition-colors group-hover:decoration-teal"
                >
                    {!! $phone1 !!}
                </span>
            </a>
        @endunless

        @unless (empty($phone2))
            <a href="tel:{!! $phone2 !!}" class="group flex gap-2">
                <x-icons.phone
                    class="size-6 text-navy transition-colors group-hover:text-teal"
                />
                <span
                    class="text-sm underline decoration-transparent transition-colors group-hover:decoration-teal"
                >
                    {!! $phone2 !!}
                </span>
            </a>
        @endunless
    </div>
</div>

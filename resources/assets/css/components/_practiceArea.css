.practiceArea {
  & ul {
    @apply tablet:columns-2;

    list-style-type: disc;
    list-style-position: outside;
    padding-left: 1.3rem;

    ::marker {
      @apply text-navy;
    }

    & li {
      @apply text-base;
    }
  }
}

.practiceAreaCarousel {
  & .gradient {
    &::before {
      @apply content-[''];
      @apply bg-gradient-to-r from-teal to-teal opacity-0 transition-all duration-300 hover:opacity-100;
      @apply absolute left-0 top-0 h-full w-full;
    }
  }

  & .image-wrapper {
    @apply absolute inset-0 opacity-0 hover:opacity-20;

    & img {
      @apply h-full w-full object-cover;
    }
  }
}

.practiceAreaButton {
  &:is(:hover, :focus) {
    & svg path {
      @apply stroke-black transition-all duration-300;
    }
  }
}

.practiceAreaCarousel {
  &:is(:hover, :focus) {
    h2 {
      @apply text-white transition-all duration-300;
    }

    & svg path {
      @apply stroke-white transition-all duration-300;
    }
  }
}

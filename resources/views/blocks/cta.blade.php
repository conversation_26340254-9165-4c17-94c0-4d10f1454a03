<x-block :block="$block" @class([
    'container',
])>
    <div
        @class([
            'flex flex-col-reverse gap-x-8 overflow-hidden rounded bg-gray',
            'tablet:grid tablet:grid-cols-10' => ! empty($image['url']),
        ])
    >
        @unless (empty($title) || empty($description))
            <div
                @class([
                    'col-span-7 space-y-6 p-6 md:p-10 lg:space-y-8 lg:p-12',
                    'tablet:order-last' => $mediaPosition !== 'right',
                ])
            >
                <div class="flex shrink-0 gap-x-4">
                    <h3 class="text-3xl text-navy tablet:text-4xl">
                        {!! $title !!}
                    </h3>
                </div>

                <div class="flex flex-col gap-y-6">
                    <p class="text-base">
                        {!! $description !!}
                    </p>
                </div>

                <div class="mt-auto flex gap-x-4">
                    {!! $children !!}
                </div>
            </div>
        @endunless

        @unless (empty($image['url']))
            <div class="col-span-3 h-full">
                <figure class="relative h-full">
                    <x-image
                        :image="$image"
                        class="h-full w-full object-cover"
                    />
                </figure>
            </div>
        @endunless
    </div>
</x-block>

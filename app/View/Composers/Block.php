<?php

namespace App\View\Composers;

use Roots\Acorn\View\Composer;
use WP_Query;

class Block extends Composer
{
    /**
     * List of views served by this composer.
     *
     * @var string[]
     */
    protected static $views = [
        'blocks/*',
        'sections/*',
        'content/*'
    ];

    /**
     * Data to be passed to view before rendering.
     *
     * @return array
     */
    public function with()
    {
        $data = $this->data->attributes ?? [];

        $data['parent'] = $this->data->block->context ?? [];
        $data['taxonomies'] = $this->getTaxonomies('job-listing');
        $data['perPageOptions'] = [8, 12, 24];
        $data['job-listing'] = $this->getJobListings();
        $data['posts'] = $this->getPosts();
        $data['categories'] = $this->getCategories();

        return $data;
    }

    private function getTaxonomies($post_type)
    {
        $taxonomies = [];
        $custom_taxonomies = get_object_taxonomies($post_type, 'objects');

        foreach ($custom_taxonomies as $taxonomy) {
            $terms = get_terms([
                'taxonomy' => $taxonomy->name,
                'orderby' => 'name',
                'hide_empty' => false, 
            ]);

            if (!is_wp_error($terms)) {
                $taxonomy->terms = $terms;
                $taxonomies[] = $taxonomy;
            }
        }

        return $taxonomies;
    }

    private function getJobListings()
    {
        $query = new WP_Query([
            'post_type'      => 'job-listing',
            'posts_per_page' => 24, 
            'post_status'    => 'publish',
        ]);
    
        $resources = $query->posts;
    
        foreach ($resources as $resource) {
            $taxonomies = wp_get_post_terms($resource->ID, get_object_taxonomies('job-listing'));
            $resource->taxonomies = $taxonomies; 
        }

        return $resources;
    }

    private function getPosts()
    {
        $query = new WP_Query([
            'post_type'      => 'post',
            'posts_per_page' => 24,
            'post_status'    => 'publish',
        ]);

        $posts = $query->posts;

        foreach ($posts as $post) {
            $categories = get_the_category($post->ID);
            $post->categories = $categories;
        }

        return $posts;
    }

    private function getCategories()
    {
        $categories = get_categories([
            'orderby' => 'name',
            'hide_empty' => false,
        ]);

        return $categories;
    }
}

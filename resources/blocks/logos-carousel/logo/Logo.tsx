import { __ } from '@wordpress/i18n';

import { useBlockProps } from '@wordpress/block-editor';

import { PlaceholderImage } from '../../components';
import { _class } from '../../components/_utils';

export default function Logo({ attributes, setAttributes }) {
  const imageRender = (
    <div {...useBlockProps()}>
      <PlaceholderImage
        image={attributes.image}
        className={_class({
          default: 'h-full w-full object-cover'
        })}
        placeholderClass={_class({
          default: 'aspect-video h-full w-full object-cover'
        })}
        imageClass={_class({
          default: 'aspect-video h-full w-full object-cover'
        })}
        onSelect={(media) => {
          setAttributes({
            image: {
              id: media.id,
              src: media.url
            }
          });
        }}
        onDelete={() => {
          setAttributes({
            image: {
              id: null,
              src: null
            }
          });
        }}
      />
    </div>
  );

  return (
    <>
      <swiper-slide class="relative h-full w-[200px] border-r border-black pr-[54px]">
        {imageRender}
      </swiper-slide>
    </>
  );
}

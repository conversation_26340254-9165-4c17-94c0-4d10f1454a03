<x-block :$block class="container py-28">
    <div
        class="flex w-full flex-col gap-y-20"
        x-data="jobOpportunities(@js($perPageOptions))"
    >
        <div
            class="job-listings flex w-full grid-cols-4 flex-wrap items-center justify-between gap-x-4 gap-y-6 tablet:grid tablet:items-start tablet:justify-start tablet:gap-x-16"
        >
            <div
                x-data="{ open: false }"
                class="relative col-span-1 row-start-1 row-end-3 tablet:w-full"
            >
                <div
                    class="fixed left-0 top-0 z-[998] h-screen w-screen bg-black/30 transition-opacity duration-300"
                    :class="open ? 'opacity-100 ' : 'opacity-0 pointer-events-none'"
                    @click="open = false"
                ></div>
                <!-- Mobile Filters Button -->
                <button
                    class="relative rounded-full bg-white px-8 py-4 shadow-xl shadow-black/5 laptop:hidden"
                    @click="open = true"
                >
                    <span class="tracking-ultra">Filters</span>
                </button>
                <!-- Sidebar / Drawer -->
                <div
                    x-cloak
                    class="sidebar fixed inset-0 z-[999] w-[85%] transform overflow-y-auto bg-white p-6 shadow-xl shadow-black/5 transition-transform duration-300 tablet:w-[50%] laptop:relative laptop:top-0 laptop:w-full laptop:bg-transparent laptop:p-0 laptop:shadow-none"
                    :class="open ? 'translate-x-0 ' : '-translate-x-full laptop:translate-x-0'"
                >
                    <div class="relative">
                        <div
                            class="flex flex-row justify-between border-b pb-6"
                        >
                            <h5 class="font-bold uppercase tracking-ultra">
                                Filters
                            </h5>
                            <button
                                @click="open = false"
                                class="block transition-opacity duration-300 hover:opacity-80 laptop:hidden"
                            >
                                @include('components.icons.close')
                            </button>
                            <button
                                @click="clearFilters()"
                                class="hidden transition-opacity duration-300 hover:opacity-80 laptop:block"
                            >
                                Clear All
                            </button>
                        </div>
                        <div class="pt-5">
                            <div
                                class="flex flex-row items-start justify-between"
                            >
                                <h5 class="pb-5 text-base font-bold">Search</h5>
                                <button
                                    @click="clearFilters()"
                                    class="block transition-opacity duration-300 hover:opacity-80 laptop:hidden"
                                >
                                    Clear All
                                </button>
                            </div>
                            <div class="relative">
                                <svg
                                    class="absolute left-0 top-[10px]"
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="25"
                                    viewBox="0 0 24 25"
                                    fill="none"
                                >
                                    <path
                                        fill-rule="evenodd"
                                        clip-rule="evenodd"
                                        d="M17.2334 16.6502L21.2334 20.655C21.412 20.8537 21.4032 21.1579 21.2134 21.3459L20.5134 22.0467C20.4195 22.1415 20.2917 22.1948 20.1584 22.1948C20.0251 22.1948 19.8973 22.1415 19.8034 22.0467L15.8034 18.0419C15.6928 17.931 15.5924 17.8104 15.5034 17.6815L14.7534 16.6803C13.5125 17.6724 11.9714 18.2126 10.3834 18.2121C7.11101 18.2235 4.26742 15.9634 3.53613 12.7699C2.80483 9.57644 4.381 6.30182 7.3315 4.88474C10.282 3.46767 13.8194 4.28634 15.8494 6.85608C17.8794 9.42582 17.8603 13.0608 15.8034 15.609L16.8034 16.2998C16.9596 16.3999 17.1038 16.5174 17.2334 16.6502ZM5.3834 11.2037C5.3834 13.9684 7.62197 16.2097 10.3834 16.2097C11.7095 16.2097 12.9813 15.6823 13.9189 14.7435C14.8566 13.8047 15.3834 12.5314 15.3834 11.2037C15.3834 8.43895 13.1448 6.19768 10.3834 6.19768C7.62197 6.19768 5.3834 8.43895 5.3834 11.2037Z"
                                        fill="#353535"
                                    />
                                </svg>
                                <input
                                    type="text"
                                    class="search w-full appearance-none rounded-md bg-transparent py-3 indent-8 focus:border focus:border-navy focus:outline-none"
                                    placeholder="Keyword..."
                                    x-model="searchQuery"
                                    @input="applyFilters()"
                                />
                            </div>
                        </div>
                        <div class="my-4 font-light">
                            Showing
                            <span x-text="filteredJobs.length"></span>
                            of
                            <span x-text="total_count"></span>
                        </div>
                    </div>

                    <!-- Province Filter -->
                    <div x-data="{ open: true }" class="accordion-section">
                        <button
                            class="flex w-full cursor-pointer flex-row items-center justify-between border-t border-black py-5"
                            @click="open = !open"
                        >
                            <h5 class="text-base font-bold">
                                Browse by Province
                            </h5>
                            <div
                                class="transform-all duration-300"
                                :class="open ? 'rotate-180' : 'rotate-0'"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="16"
                                    height="17"
                                    viewBox="0 0 16 17"
                                    fill="none"
                                >
                                    <path
                                        fill-rule="evenodd"
                                        clip-rule="evenodd"
                                        d="M8.68542 11.6667C8.5048 11.8473 8.21201 11.8473 8.03139 11.6667L3.31612 6.95139C3.13551 6.77077 3.13551 6.47798 3.31612 6.29736L3.53415 6.07931C3.71476 5.89869 4.0076 5.89869 4.18821 6.07931L8.3584 10.2495L12.5286 6.07931C12.7092 5.89869 13.002 5.89869 13.1826 6.07931L13.4007 6.29736C13.5813 6.47798 13.5813 6.77077 13.4007 6.95139L8.68542 11.6667Z"
                                        fill="#353535"
                                    />
                                </svg>
                            </div>
                        </button>
                        <div
                            x-show="open"
                            x-collapse
                            class="accordion-content scrollbar mb-4 flex max-h-[450px] flex-col space-y-4 overflow-y-auto"
                        >
                            <select
                                class="ml-1 cursor-pointer rounded-none border-b border-black bg-transparent px-0 py-3 transition-opacity duration-300 hover:opacity-70 focus:outline-none"
                                x-model="selectedFilters['location']"
                                @change="applyFilters()"
                            >
                                <option value="">All Provinces</option>
                                <template
                                    x-for="term in taxonomyData"
                                    :key="term.id"
                                >
                                    <option
                                        :value="term.slug"
                                        x-text="term.title"
                                    ></option>
                                </template>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div
                class="col-span-4 row-span-1 flex h-5 justify-end laptop:col-span-3"
            >
                <div class="col-start-3 flex w-full items-center justify-end">
                    <label for="perPage">Show per page:</label>
                    <div class="relative">
                        <select
                            class="ml-1 w-[50px] cursor-pointer appearance-none rounded-none border-b border-black bg-transparent transition-opacity duration-300 hover:opacity-70 focus:outline-none"
                            id="perPage"
                            x-model="perPage"
                            @change="applyFilters()"
                        >
                            @foreach ($perPageOptions as $option)
                                <option value="{{ $option }}">
                                    {{ $option }}
                                </option>
                            @endforeach
                        </select>
                        <svg
                            class="pointer-events-none absolute right-0 top-[0]"
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="25"
                            viewBox="0 0 24 25"
                            fill="none"
                        >
                            <path
                                fill-rule="evenodd"
                                clip-rule="evenodd"
                                d="M12.3977 16.0335C12.178 16.2532 11.8219 16.2532 11.6022 16.0335L5.86739 10.2986C5.64772 10.079 5.64772 9.72287 5.86739 9.5032L6.13256 9.238C6.35222 9.01832 6.70838 9.01832 6.92805 9.238L12 14.3099L17.0719 9.238C17.2916 9.01832 17.6477 9.01832 17.8674 9.238L18.1326 9.5032C18.3522 9.72287 18.3522 10.079 18.1326 10.2986L12.3977 16.0335Z"
                                fill="#353535"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            <div
                class="jobs col-span-4 grid w-full flex-none auto-rows-max grid-cols-4 gap-4 tabletLg:grid-cols-3 laptop:col-span-3"
                x-init="applyFilters()"
            >
                <div
                    x-show="!jobs || jobs.length === 0"
                    class="col-span-2 mt-20 py-4 text-center"
                >
                    <span>Loading jobs...</span>
                </div>
                <div
                    x-show="jobs && jobs.length > 0 && (! filteredJobs || filteredJobs.length === 0)"
                    class="col-span-2 mt-20 py-4 text-center"
                >
                    <span>No jobs match your criteria.</span>
                    <button
                        @click="clearFilters()"
                        class="mt-4 rounded bg-teal px-4 py-2 text-white"
                    >
                        Clear Filters
                    </button>
                </div>
                <template x-for="(job, index) in filteredJobs" :key="index">
                    <div
                        class="job-card col-span-4 flex flex-col justify-between overflow-clip rounded border border-navy mobile:col-span-2 tabletLg:col-span-1"
                    >
                        {{--
                            <div class="flex flex-col gap-2">
                            <template x-if="job.job_id">
                            <span
                            class="w-fit rounded rounded-b-none rounded-r-none rounded-t-none bg-teal px-2 text-sm uppercase"
                            x-text="'ID# ' + job.job_id"
                            ></span>
                            </template>
                            </div>
                        --}}

                        <div class="space-y-2 p-3">
                            <h5 class="text-sm font-bold">
                                <span
                                    x-show="job.company.length > 0"
                                    x-html="job.company"
                                ></span>
                                <span
                                    class="block text-xs font-normal not-italic"
                                    x-html="job.title"
                                ></span>
                            </h5>

                            <template x-if="job.logo">
                                <figure class="mt-4 w-fit">
                                    <img
                                        class="max-h-[75px] max-w-full"
                                        :src="job.logo"
                                    />
                                </figure>
                            </template>

                            <template
                                x-if="job.city_taxonomy.length > 0 || job.province_taxonomy.length > 0"
                            >
                                <dl class="flex gap-2 text-xs">
                                    <dt class="sr-only">Location:</dt>
                                    <dd
                                        class="flex flex-row items-start font-bold"
                                    >
                                        <span
                                            x-html="job.city_taxonomy.length > 0 ? job.city_taxonomy[0].title : ''"
                                        ></span>
                                        <span
                                            x-html="job.province_taxonomy.length > 0 ? ', ' + job.province_taxonomy[0].title : ''"
                                        ></span>
                                    </dd>
                                </dl>
                            </template>

                            <div class="w-full pt-4">
                                <a
                                    :href="job.link"
                                    class="group inline-flex flex-row items-baseline border-b border-b-transparent no-underline transition-all duration-300 hover:border-b-black"
                                >
                                    <span class="mr-2 uppercase">
                                        Learn More
                                    </span>
                                    <span class="h-3.5 w-3.5">
                                        @include('components.icons.squareArrow')
                                    </span>
                                </a>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <div
            class="flex w-full flex-col items-center justify-between desktop:gap-4"
        >
            <div
                class="col-start-2 flex min-w-fit items-center justify-center space-x-2 justify-self-start"
            >
                <div class="flex w-full">
                    <button
                        @click="prevPage"
                        :disabled="page === 1"
                        class="px-2 py-2 laptop:px-4 laptop:py-2"
                        aria-label="Previous Page"
                    >
                        <svg
                            class="rotate-180"
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="17"
                            viewBox="0 0 16 17"
                            fill="none"
                        >
                            <path
                                d="M5.35008 15.4974L4.16675 14.3141L9.65008 8.83073L4.16675 3.3474L5.35008 2.16406L12.0167 8.83073L5.35008 15.4974Z"
                                fill="#353535"
                                fill-opacity="0.7"
                            />
                        </svg>
                    </button>

                    <template
                        x-for="p in pagesToShow"
                        :key="p + (typeof p === 'string' ? '_ellipsis' : '')"
                    >
                        <button
                            @click="goToPage(p)"
                            x-text="p"
                            :class="{ 'border-b-teal border-b-4 text-black': page === p, 'border-gray border-b-2 text-grey': page !== p }"
                            class="px-4 py-3 laptop:px-8 laptop:py-6"
                            :disabled="p === '...'"
                            :aria-label="p === '...' ? 'Ellipsis' : `Go to page ${p}`"
                        ></button>
                    </template>

                    <button
                        @click="nextPage"
                        :disabled="page === totalPages"
                        class="px-2 py-2 laptop:px-4 laptop:py-2"
                        aria-label="Next Page"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="17"
                            viewBox="0 0 16 17"
                            fill="none"
                        >
                            <path
                                d="M5.35008 15.4974L4.16675 14.3141L9.65008 8.83073L4.16675 3.3474L5.35008 2.16406L12.0167 8.83073L5.35008 15.4974Z"
                                fill="#353535"
                                fill-opacity="0.7"
                            />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
</x-block>

<x-block :block="$block" class="container">
    <div class="relative flex flex-col gap-y-14 lg:gap-y-16">
        @unless (empty($attributes['eyebrow']))
            <div
                class="flex w-full flex-col border-t border-current pt-3 laptop:flex-row"
            >
                <div class="flex flex-col gap-y-8">
                    <div class="flex items-start gap-3">
                        <span
                            class="h-5 w-5 flex-none rounded-full bg-teal"
                        ></span>
                        <p class="text-base font-bold uppercase tracking-ultra">
                            {!! $attributes['eyebrow'] !!}
                        </p>
                    </div>
                </div>
            </div>
        @endunless

        <div class="flex flex-col gap-4 laptop:flex-row">
            @unless (empty($attributes['title']))
                <div class="flex w-full shrink-0 gap-x-4 laptop:w-1/3">
                    <h2 class="text-3xl text-navy tablet:text-4xl">
                        {!! $attributes['title'] !!}
                    </h2>
                </div>
            @endunless

            <div
                class="flex w-full min-w-[min(100%,300px)] flex-col gap-y-6 laptop:w-2/3"
            >
                @unless (empty($attributes['description']))
                    <p class="text-base">
                        {!! $attributes['description'] !!}
                    </p>
                @endunless

                <div class="flex gap-x-4">
                    {!! $children !!}
                </div>
            </div>
        </div>
    </div>
</x-block>

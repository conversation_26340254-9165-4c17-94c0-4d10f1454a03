<?php

namespace App\Livewire;

use Livewire\Component;

class TeamIndex extends Component
{
    use HasPagination;

    public $exclude = [];

    public $sortBy = 'title';

    protected $perPage = 12;

    public function mount($exclude = [])
    {
        $exclude = is_array($exclude) ? $exclude : [];

        $this->exclude = $exclude;
    }

    public function getTeamMembers()
    {
        $queryArgs = [
            'post_type' => 'team-member',
            'paged' => $this->page,
            'posts_per_page' => $this->perPage,
        ];

        if ($this->sortBy === 'title') {
            $queryArgs['orderby'] = 'title';
            $queryArgs['order'] = 'ASC';
        }

        if (! empty($this->exclude)) {
            $queryArgs['tax_query'] = [
                [
                    'taxonomy' => 'position',
                    'field' => 'id',
                    'terms' => $this->exclude,
                    'operator' => 'NOT IN',
                ],
            ];
        }

        return new \WP_Query($queryArgs);
    }

    public function render()
    {
        $teamMembersQuery = $this->getTeamMembers();

        $teamMembers = collect($teamMembersQuery->posts)
            ->map(function ($post) {
                return new \App\Models\TeamMember($post);
            });

        if ($this->sortBy === 'position') {
            $teamMembers = $teamMembers->sortBy(function ($member) {
                if (empty($member->position)) {
                    return false;
                }

                return $member->position->name;
            }, SORT_NATURAL);
        }

        $data = [
            'teamMembers' => $teamMembers,
            'pagination' => $this->getPagination($teamMembersQuery)
        ];

        return view('livewire.team-index', $data);
    }
}

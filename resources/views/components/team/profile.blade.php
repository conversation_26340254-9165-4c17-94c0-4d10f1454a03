@props([
    'id' => null,
    'full' => false,
])

@php
    $teamMember = new App\Models\TeamMember($id);
@endphp

<div class="has-circle flex">
    <div class="flex flex-1 flex-col gap-2">
        <figure x-data="{ open: false }" x-id="['team-member']">
            <div class="relative">
                @unless (empty($teamMember->featured_image))
                    <x-image
                        :image="$teamMember->featured_image"
                        class="aspect-square w-full rounded bg-white object-cover object-top"
                    />
                @endif

                @if ($full)
                    <button
                        class="absolute bottom-0 left-0 z-50 aspect-square bg-teal p-1 text-black transition-colors hover:bg-navy hover:text-white"
                        x-on:click="open = true"
                    >
                        <x-icons.plus />
                    </button>

                    <template x-teleport="body">
                        <x-team.modal :team-member="$teamMember" />
                    </template>
                @endif
            </div>
        </figure>

        <div class="flex flex-col gap-4">
            <div class="flex flex-col">
                <div class="pt-4 text-base font-bold leading-7">
                    {!! $teamMember->name !!}
                </div>

                @unless (empty($teamMember->position))
                    <div class="text-base">
                        {!! $teamMember->position->name !!}
                    </div>
                @endunless
            </div>

            @unless (empty($teamMember->short_bio))
                <div class="text-sm">{!! $teamMember->short_bio !!}</div>
            @endunless
        </div>

        <div class="flex gap-2">
            @unless (empty($teamMember->email))
                <div class="flex flex-col gap-4">
                    <a
                        class="text-navy transition-colors hover:text-teal"
                        href="mailto:{{ $teamMember->email }}"
                    >
                        <x-icons.mail class="h-6 w-6 flex-none text-current" />
                    </a>
                </div>
            @endunless

            @unless (empty($teamMember->linkedin))
                <a
                    class="text-navy transition-colors hover:text-teal"
                    href="{{ $teamMember->linkedin }}"
                    target="_blank"
                >
                    <x-icons.linkedin class="h-6 w-6 flex-none text-current" />
                </a>
            @endunless
        </div>
    </div>
</div>

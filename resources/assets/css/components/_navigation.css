#site-header {
  .wp-block-navigation {
    & > .wp-block-navigation__container {
      display: flex;
      gap: 1rem;

      @media (min-width: theme('screens.xl')) {
        gap: 2rem;
      }

      .wp-block-navigation-item__content {
        font-size: 1rem;
        letter-spacing: theme('letterSpacing.wide');
      }

      .wp-block-navigation-item {
        .wp-block-navigation-submenu__toggle {
          svg {
            @apply transition-transform duration-300;

            translate: 0 -2px;
          }
          &:is([aria-expanded='true']) {
            svg {
              transform: rotate(180deg);
            }
          }
        }
      }

      .wp-block-navigation__submenu-container {
        top: calc(100% + 2.75rem);
        background: theme('colors.lightGray');
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;

        &:before {
          content: '';
          display: block;
          width: 100%;
          height: 2.75rem;
          position: absolute;
          top: 0;
          transform: translateY(-100%);
        }

        .wp-block-navigation-item {
          padding: 0;

          .wp-block-navigation-item__content {
            @apply transition-colors duration-300;

            min-width: 13rem;
            padding: 1rem;
            background-color: transparent;

            &:hover {
              background-color: theme('colors.gray');
              text-decoration: none;
            }
          }
        }
      }

      @media not all and (min-width: theme('screens.lg')) {
        display: block;
        padding-left: 1.5rem;
        padding-right: 1.5rem;
        font-size: 1.5rem;

        & > .wp-block-navigation-item {
          width: 100%;
          padding-top: 2rem;
          padding-bottom: 2rem;
          display: flex;
          flex-wrap: wrap;
          border-top: 1px solid theme('colors.black');

          :is(.wp-block-navigation-item__content) {
            font-size: 1.5rem;
            flex: 1 1 0%;
          }

          .wp-block-navigation__submenu-container {
            position: static;
            flex: 1 0 100%;
            transform: none;
            top: 100%;
            background-color: transparent;
            border: none;
            padding: 0;

            & > .wp-block-navigation-item {
              width: 100%;

              & > .wp-block-navigation-item__content {
                padding-left: 0;
                padding-right: 0;
                padding-top: 1.5rem;
                padding-bottom: 1.5rem;
              }
            }
          }
        }
      }
    }
  }
}

.wp-block-navigation-submenu {
  @apply z-20 flex gap-1;
}

.wp-block-navigation .wp-block-navigation__submenu-icon {
  @apply h-4 w-4;
}

.wp-block-navigation :where(a) {
  @apply no-underline hover:underline;
}

.wp-block-navigation__responsive-container-open:has(
    + .wp-block-navigation__responsive-container:is(.is-menu-open)
  ) {
  @apply opacity-0 transition-opacity duration-300;
}

.wp-block-navigation__responsive-container-open:has(
    + .wp-block-navigation__responsive-container:not(.is-menu-open)
  ) {
  @apply opacity-100 transition-opacity duration-300;
}

.wp-block-navigation:not(.has-background)
  .wp-block-navigation__responsive-container.is-menu-open {
  /* padding-left: 2.4rem;
  padding-right: 2.4rem;
  padding-top: 3rem;
  top: 2.7rem;
  background: transparent; */
  ul {
    @apply mt-16 w-full;

    &.wp-block-navigation-submenu {
      @apply mt-8;
    }

    li {
      &.wp-block-navigation-item {
        @apply w-full border-t border-black py-8 text-xl;
      }

      &.nav-button {
        a {
          @apply button-primary-base overflow-hidden text-nowrap rounded-full border-none !px-8 !py-4 text-base uppercase !text-white no-underline transition-all duration-300 hover:no-underline;
        }
      }
    }

    &.wp-block-navigation-submenu {
      li {
        &.wp-block-navigation-item {
          @apply w-full border-none py-8 text-xl;
        }
      }
    }
  }

  .wp-block-navigation__responsive-close {
    @apply z-[2] h-full bg-[#C8D6E1];
  }
}

@media (max-width: 768px) {
  body .wp-block-navigation__responsive-container-open:not(.always-shown) {
    display: block !important;
  }
  body
    .wp-block-navigation__responsive-container:not(.hidden-by-default):not(
      .is-menu-open
    ) {
    display: none !important;
  }

  .wp-block-navigation__responsive-container.is-menu-open {
    @apply z-[99];
  }

  button.wp-block-navigation__responsive-container-close {
    @apply -top-11 z-[3] p-6;
  }

  .wp-block-navigation__responsive-container-content {
    @apply mt-16 h-full px-6 pb-16 pt-16;
  }

  .wp-block-navigation__responsive-dialog {
    margin-top: 0;
    height: 100%;
  }

  .mobile-menu.wp-block-navigation {
    @apply h-full w-full;

    .wp-block-navigation-item:not(.btn-primary) {
      @apply w-full border-t border-black py-8 text-xl;

      .wp-block-navigation-item__content:has(
          + .wp-block-navigation-submenu__toggle[aria-expanded='true']
        ) {
        @apply font-bold;
      }
    }

    .wp-block-navigation-item:is(.btn.btn-primary) {
      @apply button-base button-primary-base mb-8 mt-auto flex-row items-center !bg-navy !text-white;
    }

    .wp-block-navigation__submenu-icon {
      @apply absolute left-auto right-0 top-10 !block;
    }

    .wp-block-navigation-submenu {
      .wp-block-navigation-item {
        @apply border-t-0 px-0 pb-0 pt-6;
      }
    }
  }

  .wp-block-navigation__responsive-container.is-menu-open
    .wp-block-navigation__submenu-container.wp-block-navigation__submenu-container.wp-block-navigation__submenu-container.wp-block-navigation__submenu-container {
    @apply w-full p-0;
  }

  .wp-block-navigation-item.wp-block-navigation-submenu {
    .wp-block-navigation-submenu__toggle:is([aria-expanded='false']) {
      ~ .wp-block-navigation-submenu {
        @apply hidden;
      }

      svg {
        @apply rotate-0 transition-transform duration-300;
      }
    }

    .wp-block-navigation-submenu__toggle:is([aria-expanded='true']) {
      svg {
        @apply rotate-180 transition-transform duration-300;
      }
    }
  }
}

@media (min-width: 768px) {
  .wp-block-navigation {
    .wp-block-navigation-item.nav-button {
      @apply !hidden;
    }
  }

  body .wp-block-navigation__responsive-container-open:not(.always-shown) {
    body .wp-block-navigation__responsive-container-open:not(.always-shown) {
      @apply !hidden;
    }
  }

  body
    .wp-block-navigation__responsive-container:not(.hidden-by-default):not(
      .is-menu-open
    ) {
    @apply !block;
  }
}

<x-block :$block class="container">
    <x-block-header :$header class="mb-6 pb-0 pt-0 md:mb-10 lg:mb-16" />

    @php
        $args = [
            'post_type' => 'practice-area',
            'posts_per_page' => -1,
            'post_status' => 'publish',
            'orderBy' => 'title',
            'order' => 'ASC',
        ];
        $practice_areas = new WP_Query($args);
    @endphp

    @if ($practice_areas->have_posts())
        @while ($practice_areas->have_posts())
            <div class="flex flex-col gap-y-6 border-t pb-10 pt-4 md:gap-y-12">
                @php
                    $practice_areas->the_post();
                    $description = get_post_meta(get_the_ID(), 'description', true);
                    $subtitle = get_post_meta(get_the_ID(), 'subtitle', true);
                    $content = get_the_content();
                @endphp

                <div
                    class="flex flex-col gap-x-8 gap-y-6 tablet:grid tablet:grid-cols-4"
                    id="{{ get_post_field('post_name', get_the_ID()) }}"
                >
                    <h2 class="w-full text-xl font-bold capitalize leading-9">
                        {!! get_the_title() !!}
                    </h2>

                    <div class="col-span-3 tablet:pt-4">
                        <div
                            class="flex flex-col gap-x-4 gap-y-4 tablet:flex-row"
                        >
                            @if (has_post_thumbnail())
                                <div
                                    class="aspect-[2/1] min-w-[min(100%,300px)] flex-none bite bite-4 tablet:w-1/3"
                                >
                                    {{ the_post_thumbnail('full', ['class' => 'w-full h-full object-cover']) }}
                                </div>
                            @endif

                            <div class="flex-1">
                                @unless (empty($description))
                                    <p class="text-sm leading-normal">
                                        {!! $description !!}
                                    </p>
                                @endunless
                            </div>
                        </div>

                        @unless (empty($subtitle) && empty($content))
                            <div
                                class="mt-6 flex flex-col gap-x-4 gap-y-4 rounded border p-4 md:mt-10 md:p-6 tablet:grid tablet:grid-cols-[30%_minmax(0,_1fr)] lg:mt-14"
                            >
                                @unless (empty($subtitle))
                                    <h3 class="text-base font-bold leading-9">
                                        {!! $subtitle !!}
                                    </h3>
                                @endunless

                                @unless (empty($content))
                                    <div class="practiceArea">
                                        {!! $content !!}
                                    </div>
                                @endunless
                            </div>
                        @endunless
                    </div>
                </div>
            </div>
        @endwhile

        @php
            wp_reset_postdata();
        @endphp
    @else
        <p>{{ __('No practice areas found.', 'takt') }}</p>
    @endif
</x-block>

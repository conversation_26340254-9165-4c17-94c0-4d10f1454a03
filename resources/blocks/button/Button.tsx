import { __ } from '@wordpress/i18n';

import { useBlockProps } from '@wordpress/block-editor';

import { Button } from '../components';
import { _class } from '../components/_utils';

export default function Edit({ attributes, setAttributes, isSelected }) {
  const { title, url, opensInNewTab, type } = attributes;

  return (
    <div {...useBlockProps({ className: 'inline-block' })}>
      <Button
        link={{
          url: url,
          title: title,
          opensInNewTab: opensInNewTab
        }}
        onChange={(value) => {
          const _button = {
            title: title,
            url: url,
            opensInNewTab: opensInNewTab,
            type: type
          };

          setAttributes({
            ..._button,
            ...value
          });
        }}
        showLinkBox={isSelected}
        focusOnMount={title.length === 0 ? 'firstElement' : false}
        variation={type}
        onVariationSelect={(value) => {
          setAttributes({
            type: value
          });
        }}
      />
    </div>
  );
}

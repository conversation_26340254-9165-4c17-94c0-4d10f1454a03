export const dark_colors = ['bg-navy', 'bg-gradient-navy-to-light-blue'];

export function isDark(color) {
  return dark_colors.includes(color);
}

export const _class = (classes) => {
  const defaultClasses = classes.default ?? '';
  const classlist = Object.keys(classes).filter((key) => {
    return key !== 'default' && classes[key] !== false;
  });

  return `${defaultClasses} ${Object.values(classlist).join(' ')}`;
};

export function sortBySelected(allItems, selectedItems) {
  const _allItems = [...allItems];

  _allItems.sort((itemA, itemB) => {
    const itemASelected = selectedItems.indexOf(itemA.id) !== -1;
    const itemBSelected = selectedItems.indexOf(itemB.id) !== -1;

    if (itemASelected && !itemBSelected) {
      return -1;
    }
    if (!itemASelected && itemBSelected) {
      return 1;
    }
    return 0;
  });

  return _allItems;
}

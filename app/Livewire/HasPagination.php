<?php

namespace App\Livewire;

use Livewire\Attributes\Url;
use Livewire\WithPagination;
use Illuminate\Pagination\LengthAwarePaginator;

trait HasPagination
{
    use WithPagination;

    #[Url(as: 'pa')]
    public int $page = 1;

    public function goToPage(int $page)
    {
        $this->page = $page;
        $this->js('$refs.posts.scrollIntoView()');
    }

    public function getPagination(\WP_Query $query)
    {
        $pagination = new LengthAwarePaginator(
            $query->posts,
            $query->found_posts,
            $this->perPage,
            $this->page
        );

        return $pagination;
    }
}

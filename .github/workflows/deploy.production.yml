name: Build and Deploy for Production

on:
  workflow_dispatch:
  push:
    branches: master

jobs:
  build:
    name: Build Assets
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: master

      - name: Install PHP Dependencies
        uses: php-actions/composer@v6

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Install Dependencies
        run: bun install --frozen-lockfile

      - name: Build Assets
        run: bun run build

      - name: Sync to <PERSON><PERSON><PERSON>
        uses: easingthemes/ssh-deploy@main
        with:
          SSH_PRIVATE_KEY: ${{ secrets.KINSTA_SSH_KEY }}
          ARGS: '-avz -i --delete'
          SOURCE: './'
          REMOTE_HOST: ${{ secrets.KINSTA_PROD_IP }}
          REMOTE_USER: ${{ secrets.KINSTA_USERNAME }}
          REMOTE_PORT: ${{ secrets.KINSTA_PROD_PORT }}
          TARGET: ${{ secrets.KINSTA_PROD_PATH }}
          EXCLUDE: '/.git/, /node_modules/, .env'

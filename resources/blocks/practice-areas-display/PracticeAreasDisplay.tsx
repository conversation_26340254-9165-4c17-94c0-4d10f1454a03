import { __ } from '@wordpress/i18n';
import {
  useBlockProps,
  store as blockEditorStore
} from '@wordpress/block-editor';
import BlockHeader from '../components/BlockHeader';
import { useSelect } from '@wordpress/data';
import PreviewImage from './PracticeAreasDisplayPreview.jpg';

export default function PracticeAreasDisplay({
  attributes,
  setAttributes,
  clientId
}) {
  const blocks = useSelect(
    (select) => select(blockEditorStore).getBlock(clientId)?.innerBlocks,
    [clientId]
  );

  return (
    <div
      {...useBlockProps({
        className: 'container relative flex flex-col gap-y-16 px-16 py-16'
      })}
    >
      <BlockHeader setAttributes={setAttributes} header={attributes.header} />

      <div className="relative inset-0 z-10 flex flex-col items-center justify-center border-t pb-48 pt-0">
        <div
          className="absolute inset-0 bg-contain bg-center bg-no-repeat opacity-10"
          style={{ backgroundImage: `url(${PreviewImage})` }}
        ></div>

        <div className="pt-32 text-center">
          <h1 className="font-heading text-5xl">Practice Areas Display</h1>
          <p className="text-gray-600 mt-4 text-lg">
            {__(
              'The Practice Areas are displayed on the front-end.',
              'text-domain'
            )}
          </p>
        </div>
      </div>
    </div>
  );
}

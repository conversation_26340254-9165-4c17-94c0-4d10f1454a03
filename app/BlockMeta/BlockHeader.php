<?php

namespace App\BlockMeta;

use Illuminate\Support\Arr;

/**
 * Setup Header Support
 */

\WP_Block_Supports::get_instance()->register('header', [
    'register_attribute' => function ($block_type) {
        if (!$block_type->attributes) {
            $block_type->attributes = [];
        }

        if (block_has_support($block_type, ['header'], false)) {
            $block_type->attributes = Arr::add(
                $block_type->attributes,
                'header.type',
                'object'
            );
            $block_type->attributes = Arr::add(
                $block_type->attributes,
                'header.default',
                [
                    'title' => '',
                    'introduction' => '',
                    'buttons' => [
                        [
                            'title' => '',
                            'url' => '',
                            'opensInNewTab' => false,
                            'type' => 'default',
                        ]
                    ]
                ]
            );
        }
    },
]);

add_filter(
    'render_block_data',
    function ($block) {
        $blockType = \WP_Block_Type_Registry::get_instance()->get_registered(
            $block['blockName']
        );

        if (!block_has_support($blockType, ['header'], false)) {
            return $block;
        }

        $blockAttributes = Arr::has($block, 'attrs') ? $block['attrs'] : [];

        $blockAttributes = Arr::add($blockAttributes, 'header.hasBreadcrumbs', false);
        $blockAttributes = Arr::add($blockAttributes, 'header.title', '');
        $blockAttributes = Arr::add($blockAttributes, 'header.introduction', '');
        $blockAttributes = Arr::add($blockAttributes, 'header.buttons', []);

        $block['attrs'] = $blockAttributes;

        return $block;
    },
    10
);

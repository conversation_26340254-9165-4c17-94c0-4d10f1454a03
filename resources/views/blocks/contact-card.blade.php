@php
    $location = $block->attributes['location'] ?? '';
    $address = $block->attributes['address'] ?? '';
    $email = $block->attributes['email'] ?? '';
    $phone1 = $block->attributes['phone1'] ?? '';
    $phone2 = $block->attributes['phone2'] ?? '';
@endphp

<div class="border-t border-current py-4 md:space-y-4">
    <h4 class="text-base font-bold max-md:mb-4">
        {!! $location !!}
    </h4>

    <address class="text-sm not-italic">{!! $address !!}</address>

    @unless (empty($email))
        <a href="mailto:{!! $email !!}" class="group flex gap-x-2 text-white">
            <x-icons.mail class="group-hover:text-teal" />
            <span
                class="text-sm underline decoration-transparent transition-colors group-hover:decoration-teal"
            >
                {!! $email !!}
            </span>
        </a>
    @endunless

    @unless (empty($phone1))
        <a href="tel:{!! $phone1 !!}" class="group flex gap-x-2 text-white">
            <x-icons.phone class="group-hover:text-teal" />
            <span
                class="text-sm underline decoration-transparent transition-colors group-hover:decoration-teal"
            >
                {!! $phone1 !!}
            </span>
        </a>
    @endunless

    @unless (empty($phone2))
        <a href="tel:{!! $phone2 !!}" class="group flex gap-x-2 text-white">
            <x-icons.phone class="group-hover:text-teal" />
            <span
                class="text-sm underline decoration-transparent transition-colors group-hover:decoration-teal"
            >
                {!! $phone2 !!}
            </span>
        </a>
    @endunless
</div>

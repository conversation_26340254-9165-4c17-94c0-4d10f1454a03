<x-block :$block class="container relative">
    <div
        role="presentation"
        class="pointer-events-none absolute left-3/4 top-0 -z-10 aspect-square w-full overflow-hidden bg-radial bg-contain bg-no-repeat"
    ></div>

    @unless (empty($attributes['eyebrow']))
        <div
            class="relative z-20 flex w-full flex-col border-t border-current pt-3 laptop:flex-row"
        >
            <div class="flex flex-col gap-y-8">
                <div class="flex items-start gap-3">
                    <span class="h-5 w-5 flex-none rounded-full bg-teal"></span>
                    <p class="text-base font-bold uppercase tracking-ultra">
                        {!! $attributes['eyebrow'] !!}
                    </p>
                </div>
            </div>
        </div>
    @endunless

    @unless (empty($title) || empty($description) || empty($formId))
        <div
            x-data="{
                init() {
                    this.$nextTick(() => {
                        const reason = new URLSearchParams(window.location.search).get(
                            'reason',
                        )
                        if (reason) {
                            this.$el.scrollIntoView({ behavior: 'smooth' })
                        }
                    })
                },
            }"
            class="z-20 flex scroll-mt-24 flex-col justify-between gap-x-20 gap-y-14 overflow-hidden tablet:grid tablet:grid-cols-2 tablet:gap-y-12"
        >
            @unless (empty($title) || empty($description))
                <div class="flex flex-col gap-y-8">
                    <div class="flex gap-x-4">
                        <h3 class="text-3xl text-navy tablet:text-4xl">
                            {!! $title !!}
                        </h3>
                    </div>

                    <div class="flex flex-col gap-y-6">
                        <p class="text-base">
                            {!! $description !!}
                        </p>
                    </div>

                    <div class="flex gap-x-4">
                        {!! $children !!}
                    </div>
                </div>
            @endunless

            @unless (empty($formId))
                <div class="flex flex-1 shrink-0">
                    <x-gform :form="$formId" />
                </div>
            @endunless
        </div>
    @endunless
</x-block>

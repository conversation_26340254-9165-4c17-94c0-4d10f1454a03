import { __ } from '@wordpress/i18n';

import {
  useBlockProps,
  RichText,
  useInnerBlocksProps,
  store as blockEditorStore
} from '@wordpress/block-editor';
import { useSelect } from '@wordpress/data';

import { _class } from '../../components/_utils';
import { Content_Blocks } from '../../components';

export default function Edit({
  attributes,
  setAttributes,
  isSelected,
  clientId
}) {
  const { title } = attributes;

  const open = useSelect(
    (select) =>
      isSelected ||
      select(blockEditorStore).hasSelectedInnerBlock(clientId, true),
    [isSelected]
  );

  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    {
      className: `prose prose-h2:text-white prose-h2:text-3xl prose-h2:font-normal prose-p:text-base mt-12 pr-12 text-white min-w-full`
    },
    {
      allowedBlocks: [...Content_Blocks],
      template: [
        ['core/heading', { placeholder: 'Title...' }],
        [
          'core/paragraph',
          {
            placeholder:
              'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat.',
            className: 'max-w-1/2'
          }
        ],
        [
          'core/list',
          {
            ordered: false,
            placeholder: 'Add a feature here...',
            className:
              'flyoutPanel--list-item pl-0 pt-4 mt-12 border-t border-white text-lg font-semibold flex flex-wrap list-none gap-x-8'
          }
        ]
      ],
      renderAppender: false
    }
  );

  return (
    <div
      {...useBlockProps({
        className: _class({
          default:
            'max-h-[475px] max-w-fit flex-1 rounded [counter-increment:item] hover:cursor-pointer hover:border-navy',
          'border border-black bg-white': !open,
          'border border-white bg-gradient-navy-to-light-blue': open
        })
      })}
    >
      <div
        className={_class({
          default:
            'flex h-full max-h-[475px] overflow-hidden hover:cursor-pointer hover:border-navy'
        })}
      >
        <div
          className={_class({
            default:
              'font-heading text-xl flex flex-col items-center justify-between px-6 py-8',
            'text-white': open,
            'text-black': !open
          })}
        >
          <span className="before:block before:content-[counter(item,decimal-leading-zero)]"></span>
          <RichText
            tagName="div"
            className={`text-xl font-normal leading-none [writing-mode:sideways-lr]`}
            placeholder={__('Title...', 'takt')}
            value={title}
            allowedFormats={[]}
            onChange={(value) => {
              setAttributes({ title: value });
            }}
            keepPlaceholderOnFocus
            disableLineBreaks={true}
          />
        </div>
        <div
          className={_class({
            default:
              'grid transition-[grid-template-columns] duration-300 ease-in-out',
            'grid-cols-[0fr]': !open,
            'grid-cols-[1fr]': open
          })}
        >
          <div className="overflow-auto">
            <div
              className={_class({
                default: 'w-[--panels-width] pb-8 pl-12'
              })}
            >
              <div {...innerBlocksProps}>{children}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

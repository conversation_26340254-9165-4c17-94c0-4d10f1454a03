<x-block :block="$block" @class([
    'container-wide container',
])>
    <div
        class="items-normal relative flex justify-between gap-3 overflow-hidden rounded bg-gradient-navy-to-light-blue px-0 md:items-end md:pl-4 md:pr-0 lg:pl-16 landscape:min-h-[529px]"
    >
        @if ($mediaType === 'image')
            @if (! empty($image['url']))
                <figure class="absolute left-0 h-full w-full">
                    <x-image
                        :image="$image"
                        class="top-0 h-full w-full object-cover opacity-40 mix-blend-multiply"
                    />
                </figure>
            @endif
        @elseif ($mediaType === 'video')
            @if ($videoSource === 'local' && ! empty($video['src']))
                <div
                    class="absolute inset-0 h-full w-full overflow-hidden bg-gradient-navy-to-light-blue"
                ></div>
                <video
                    class="absolute inset-0 h-full w-full object-cover opacity-60"
                    autoplay
                    loop
                    muted
                    playsinline
                >
                    <source
                        src="{{ $video['src'] }}"
                        type="{{ $video['mime'] }}"
                    />
                </video>
            @elseif (! empty($videoUrl))
                <div
                    class="absolute inset-0 h-full w-full overflow-hidden bg-gradient-navy-to-light-blue"
                ></div>
                <div
                    className="absolute inset-0 h-full w-full overflow-hidden bg-gradient-navy-to-light-blue"
                ></div>
                <iframe
                    src="{!! $videoUrl !!}?controls=0&autoplay=1&loop=1&mute=1"
                    class="pointer-events-none absolute left-1/2 top-1/2 h-[56.25vw] min-h-full w-[177.77777778vh] min-w-full -translate-x-1/2 -translate-y-1/2 opacity-60"
                    allow="autoplay; fullscreen"
                    allowfullscreen
                ></iframe>
            @endif
        @endif
        <div
            class="z-10 flex w-full flex-col items-end justify-between laptop:flex-row landscape:h-full landscape:min-h-80"
        >
            @if ($hasBreadcrumbs || $title)
                <div
                    class="w-full flex-col justify-end gap-y-6 px-6 pb-16 pt-6 laptop:flex laptop:self-end laptop:pl-0 laptop:pr-6 laptop:pt-28 portrait:pb-48"
                >
                    @if ($hasBreadcrumbs)
                        <div class="flex items-start gap-3">
                            <span
                                class="h-5 w-5 flex-none rounded-full bg-teal"
                            ></span>
                            @php(yoast_breadcrumb('<p class="text-white text-sm font-bold uppercase tracking-ultra" id="breadcrumbs">', '</p>'))
                        </div>
                    @endif

                    @unless (empty($title))
                        <h1
                            class="mt-6 text-4xl/none text-white lg:text-heading"
                        >
                            {!! $title !!}
                        </h1>
                    @endunless
                </div>
            @endif

            <div
                class="flex h-fit w-full shrink-0 flex-col justify-end gap-y-4 break-words rounded rounded-b-none bg-teal p-6 text-base first:*:flex-1 only:last:*:flex-none lg:gap-y-8 laptop:w-96 laptop:pb-16 laptop:pt-24 landscape:h-auto landscape:self-stretch"
            >
                {!! $children !!}
            </div>
        </div>
    </div>
</x-block>

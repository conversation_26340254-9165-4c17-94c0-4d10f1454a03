@php
    $job = new \App\Models\WPBasePost(get_the_ID());
    $city = get_the_terms($job->ID, 'city');
    $province = get_the_terms($job->ID, 'province');
@endphp

<x-block :$block class="not-prose mb-16 sm:max-w-none">
    <div class="container container-wide">
        <div class="relative gap-6 overflow-hidden rounded bg-gray">
            <div class="flex justify-between gap-6">
                <div
                    class="flex-1 px-[--wide-gutter] py-6 lg:px-16 lg:pb-16 lg:pt-28"
                >
                    <div class="mb-8 flex items-center gap-3">
                        <span
                            class="block h-5 w-5 flex-none rounded-full bg-teal"
                        ></span>
                        @php(yoast_breadcrumb('<p class="text-sm font-bold text-black uppercase tracking-ultra not-prose" id="breadcrumbs">', '</p>'))
                    </div>
                    <h2 class="text-2xl text-navy tablet:text-3xl">
                        @unless (empty($job->getMeta('company')))
                            <small
                                class="mb-2 block text-3xl not-italic tablet:text-4xl"
                            >
                                {!! $job->getMeta('company') !!}
                            </small>
                        @endunless

                        {!! $job->title !!}
                    </h2>
                    {{--
                        <p class="mt-2 text-base">
                        @if (empty($job->excerpt))
                        {!! $description ?? '' !!}
                        @else
                        {!! $job->excerpt !!}
                        @endif
                        </p>
                    --}}
                    @unless (empty($job->featured_image))
                        <figure class="mt-4 w-fit md:mt-12">
                            <x-image
                                class="max-h-[75px] max-w-full"
                                :src="$job->featured_image['url']"
                                :image="$job->featured_image"
                            />
                        </figure>
                    @endunless

                    <dl class="my-8 flex gap-x-4 border-b pb-6">
                        {{--
                            @unless (empty($job->getMeta('job_id')))
                            <div class="inline-flex bg-teal p-2 text-black">
                            <dt>ID#</dt>
                            <dd class="ml-[1ch]">
                            {!! $job->getMeta('job_id') !!}
                            </dd>
                            </div>
                            @endunless
                        --}}

                        @unless (empty($city))
                            <dt class="sr-only">Location</dt>
                            <dd class="inline">
                                {!! $city[0]->name . (! empty($province) ? ', ' . (get_term_meta($province[0]->term_id, 'takt-job-province-code', true) ?: $province[0]->name) : '') !!}
                            </dd>
                        @endunless
                    </dl>
                    <div class="mt-8 flex gap-x-4 empty:hidden">
                        {!! $children !!}
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-block>

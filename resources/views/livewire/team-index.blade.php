<div x-ref="posts" class="scroll-mt-20">
    <div class="mb-4">
        <select
            wire:model.live="sortBy"
            class="border-0 bg-transparent text-sm outline-0"
        >
            <option value="" disabled selected>Sort by</option>
            <option value="title">Sort by Name</option>
            <option value="position">Sort by Position</option>
        </select>
    </div>
    <x-team.grid>
        @foreach ($teamMembers as $member)
            <li
                wire:animate-move
                wire:fade-in
                wire:key="post-{{ $member->ID }}"
            >
                <x-team.profile :id="$member->ID" :full="false" />
            </li>
        @endforeach
    </x-team.grid>
    <div class="mt-16">
        @include('livewire.pagination')
    </div>
</div>

<x-block :$block class="container">
    <x-block-header :$header class="pb-14 lg:pb-16" />
    <div
        x-data="{
            activePanel: null,

            curWidth: 0,

            async init() {
                await this.$nextTick(() => {
                    this.setPanelWidth()
                })

                this.$watch('curWidth', (value) => {
                    this.$nextTick(() => {
                        this.setPanelWidth()
                    })
                })

                this.activePanel = this.$el.children[0].id
            },

            setPanelWidth() {
                this.activePanel = null

                const panelsWidth =
                    this.$el.getBoundingClientRect().right -
                    this.$el.children[0].getBoundingClientRect().left
                const remainingWidth = this.$el.clientWidth - panelsWidth

                this.$el.style.setProperty('--panels-width', `${remainingWidth}px`)
            },
        }"
        x-resize="curWidth = $width"
        class="flex items-stretch justify-end gap-4 [counter-reset:item] max-lg:flex-col"
    >
        {!! $children !!}
    </div>
</x-block>

import { MediaUpload } from '@wordpress/block-editor';
import {
  Icon,
  Button,
  Popover,
  TextControl,
  __experimentalVStack as VStack,
  __experimentalHStack as HStack
} from '@wordpress/components';
import { useEntityProp } from '@wordpress/core-data';
import { memo, useRef, useState } from '@wordpress/element';

const SocialLinks: React.FC<{ className?: string }> = memo(({ className }) => {
  const [socials, setSocials] = useEntityProp('root', 'site', 'Social Links');

  const [isEditingSocial, setIsEditingSocial] = useState<number | null>(null);

  return (
    <div className={`${className}`}>
      {socials &&
        socials.map((social, index) => (
          <div className="relative px-2" key={social.title}>
            <button
              onClick={() => {
                const _socials = socials.toSpliced(index, 0, {
                  title: '',
                  url: '',
                  icon: {
                    src: null,
                    id: null
                  }
                });

                setSocials(_socials);
              }}
              className="group absolute inset-y-0 left-0 z-50 h-full w-1 bg-white opacity-0 transition-opacity hover:opacity-100"
              title="Add Logo"
            >
              <Icon
                icon="plus"
                size={8}
                className="absolute left-1/2 top-1/2 flex !size-4 -translate-x-1/2 -translate-y-1/2 items-center justify-center bg-white opacity-0 transition-opacity group-hover:opacity-100"
              />
            </button>
            <SocialIcon
              item={social}
              editing={isEditingSocial == index}
              close={() => setIsEditingSocial(null)}
              setEditing={() => setIsEditingSocial(index)}
              updateItem={(value) => {
                const _socials = [...socials];

                _socials[index] = value;

                setSocials(_socials);
              }}
              onDelete={() => {
                const _socials = [...socials];
                _socials.splice(index, 1);

                setSocials(_socials);
                setIsEditingSocial(null);
              }}
            />
          </div>
        ))}
      <Button
        icon="plus"
        className="size-8 bg-white opacity-60 transition-opacity hover:opacity-100"
        onClick={() => {
          setSocials([
            ...socials,
            {
              title: '',
              url: '',
              icon: {
                src: null,
                id: null
              }
            }
          ]);
        }}
        label="New Social"
      />
    </div>
  );
});

const SocialIcon: React.FC<{
  item: {
    title: string;
    url: string;
    icon: {
      id: number;
      src: string;
    };
  };
  close: () => void;
  editing: boolean;
  setEditing: () => void;
  updateItem: (value: any) => void;
  onDelete: () => void;
}> = memo(
  ({ item, close, editing = false, setEditing, updateItem, onDelete }) => {
    const [itemRef, setItemRef] = useState();
    const [tempTitle, setTempTitle] = useState(item.title);
    const [tempUrl, setTempUrl] = useState(item.url);
    const [tempIcon, setTempIcon] = useState<{ id: number; src: string }>({
      id: null,
      src: null
    });
    const [mediaOpen, setMediaOpen] = useState(false);

    return (
      <div className="group/icon relative" ref={setItemRef}>
        {item.icon.src ? (
          <img className="h-6 w-6" src={item.icon.src} />
        ) : (
          <div className="h-6 w-6">
            <svg
              className={`relative z-10 h-full w-full max-w-xs object-contain text-[#DADADA] transition-colors group-hover/icon:text-white/50`}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 31 27"
              fill="none"
              preserveAspectRatio="xMidYMid meet"
            >
              <path
                d="M28.3988 0.66333H2.602C1.98002 0.66333 1.38352 0.909227 0.943718 1.34693C0.503915 1.78462 0.256836 2.37827 0.256836 2.99727V24.0027C0.256836 24.6217 0.503915 25.2154 0.943718 25.6531C1.38352 26.0908 1.98002 26.3367 2.602 26.3367H28.3988C29.0208 26.3367 29.6173 26.0908 30.0571 25.6531C30.4969 25.2154 30.7439 24.6217 30.7439 24.0027V2.99727C30.7439 2.37827 30.4969 1.78462 30.0571 1.34693C29.6173 0.909227 29.0208 0.66333 28.3988 0.66333ZM28.3988 2.99727V17.9856L24.5776 14.1841C24.3599 13.9674 24.1013 13.7954 23.8167 13.6781C23.5322 13.5607 23.2272 13.5004 22.9192 13.5004C22.6111 13.5004 22.3061 13.5607 22.0216 13.6781C21.737 13.7954 21.4785 13.9674 21.2607 14.1841L18.3292 17.1016L11.88 10.6832C11.4403 10.2459 10.844 10.0002 10.2223 10.0002C9.6006 10.0002 9.00433 10.2459 8.56457 10.6832L2.602 16.6173V2.99727H28.3988ZM2.602 19.9183L10.2238 12.333L21.9496 24.0027H2.602V19.9183ZM28.3988 24.0027H25.2665L19.9899 18.7514L22.9214 15.8339L28.3988 21.2866V24.0027ZM17.8456 9.41561C17.8456 9.0694 17.9487 8.73097 18.142 8.44311C18.3352 8.15525 18.6099 7.93089 18.9313 7.7984C19.2527 7.66591 19.6064 7.63125 19.9476 7.69879C20.2887 7.76633 20.6021 7.93304 20.8481 8.17785C21.0941 8.42266 21.2616 8.73456 21.3295 9.07411C21.3974 9.41367 21.3625 9.76563 21.2294 10.0855C21.0963 10.4053 20.8708 10.6787 20.5816 10.8711C20.2924 11.0634 19.9523 11.1661 19.6044 11.1661C19.1379 11.1661 18.6906 10.9816 18.3607 10.6534C18.0309 10.3251 17.8456 9.87986 17.8456 9.41561Z"
                fill="currentColor"
              />
            </svg>
          </div>
        )}
        <Button
          variant="primary"
          icon="edit"
          className="absolute inset-0 z-50 !m-0 grid !h-full w-full place-items-center border-2 border-solid border-white text-white opacity-0 transition group-hover/image:opacity-100"
          onClick={() => setEditing()}
        >
          Edit
        </Button>
        {editing && (
          <Popover
            className="takt-popover"
            offset={mediaOpen ? 9999 : 24}
            placement={'bottom'}
            anchor={itemRef}
            shift={!mediaOpen}
            focusOnMount={true}
          >
            <div className="bg-white p-5">
              <VStack spacing={4} alignment="bottom">
                <TextControl
                  className="w-72 [&>.components-base-control\_\_field]:mb-0"
                  label="Title"
                  value={tempTitle}
                  onChange={(value) => setTempTitle(value)}
                />
                <TextControl
                  className="w-72 [&>.components-base-control\_\_field]:mb-0"
                  label="Url"
                  value={tempUrl}
                  onChange={(value) => setTempUrl(value)}
                />
                <HStack spacing={2}>
                  <MediaUpload
                    onSelect={(media) => {
                      setTempIcon({
                        id: media.id,
                        src: media.url
                      });
                      setMediaOpen(false);
                    }}
                    onClose={() => setMediaOpen(false)}
                    allowedTypes={['image']}
                    value={item.icon.id}
                    render={({ open }) => (
                      <Button
                        onClick={() => {
                          open();
                          setMediaOpen(true);
                        }}
                        variant="primary"
                      >
                        {item.icon.id ? 'Replace Icon' : 'Add Icon'}
                      </Button>
                    )}
                  />
                  <Button
                    variant="primary"
                    onClick={() => {
                      updateItem({
                        title: tempTitle,
                        url: tempUrl,
                        icon: tempIcon
                      });

                      close();
                    }}
                  >
                    Save
                  </Button>
                  <Button variant="secondary" onClick={() => onDelete()}>
                    Remove
                  </Button>
                </HStack>
              </VStack>
            </div>
          </Popover>
        )}
      </div>
    );
  }
);

export default SocialLinks;

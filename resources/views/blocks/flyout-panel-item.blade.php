<div
    x-id="['flyout-panel']"
    :id="$id('flyout-panel')"
    x-data="{
        id: $id('flyout-panel'),

        get open() {
            return this.id === this.activePanel
        },

        set open(value) {
            this.activePanel = value ? this.id : null
        },
    }"
    class="relative min-h-0 max-w-fit flex-1 rounded border [counter-increment:item] hover:border-navy max-laptop:max-w-full laptop:max-w-fit"
    :class="{
        'laptop:border-b border-black bg-lightGray': !open,
        'border-white bg-gradient-navy-to-light-blue hover:border-white hover:text-navy min-h-fit': open
    }"
>
    <div
        class="min-h-full lg:flex"
        :class="{
            'overflow-hidden': ! open,
            'overflow-auto ': open
        }"
    >
        <div
            class="text-xl relative flex items-center justify-between px-4 py-4 md:px-6 md:py-6 lg:py-8 laptop:flex-col"
            :class="{
                'text-white': open,
                'text-black hover:text-navy': !open
            }"
        >
            <span
                aria-hidden="true"
                class="flex-none before:block before:content-[counter(item,decimal-leading-zero)]"
            ></span>
            <h3
                aria-hidden="true"
                class="leading-none lg:absolute lg:inset-y-0 lg:left-0 lg:h-full lg:w-full"
            >
                <span
                    class="origin-bottom-left lg:absolute lg:bottom-6 lg:left-14 lg:block lg:w-max lg:-rotate-90 lg:whitespace-nowrap"
                >
                    {!! $title !!}
                </span>
            </h3>
            <button @click="open = !open" class="absolute inset-0">
                <span class="sr-only">{!! $title !!}</span>
            </button>
        </div>
        <div
            class="grid transition-all duration-300 ease-in-out max-lg:grid-rows-[0fr] lg:grid-cols-[0fr]"
            :class="{
                'max-lg:grid-rows-[0fr] lg:grid-cols-[0fr]': !open,
                'max-lg:grid-rows-[1fr] lg:grid-cols-[1fr]': open,
            }"
        >
            <div class="overflow-y-auto overflow-x-clip">
                <div
                    class="w-full px-6 py-8 laptop:w-[--panels-width] laptop:px-12"
                >
                    <div class="prose prose-invert contents w-full">
                        {!! $children !!}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<x-block :$block class="container">
    <div class="flex flex-col">
        <x-block-header :header="$header" class="mb-6 md:mb-10 lg:mb-16" />

        <div
            class="border-t border-black"
            x-data="slider({
                        autoplay: {
                            delay: 0,
                            disableOnInteraction: false,
                            pauseOnMouseEnter: true,
                        },
                        loop: true,
                        slidesPerView: 'auto',
                        speed: 5000,
                        allowTouchMove: false,
                        spaceBetween: 54,
                        breakpoints: {
                            1024: {
                                slidesPerView: 5,
                            },
                        },
                    })"
            x-init="
                $nextTick(() => {
                    const wrapper = $el.querySelector('.swiper-wrapper')
                    const slides = [...$el.querySelectorAll('.swiper-slide')]

                    wrapper.innerHTML = ''

                    slides.sort(() => Math.random() - 0.5)
                    slides.forEach((slide) => wrapper.appendChild(slide))

                    const duplicates = slides.map((slide) => slide.cloneNode(true))
                    duplicates.forEach((slide) => wrapper.appendChild(slide))
                    swiper.update()
                })
            "
        >
            <div class="swiper overflow-visible">
                <div class="swiper-wrapper ease-linear">
                    {!! $children !!}
                </div>
            </div>
        </div>
    </div>
</x-block>

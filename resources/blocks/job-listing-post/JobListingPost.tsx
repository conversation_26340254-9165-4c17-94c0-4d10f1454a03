import { __ } from '@wordpress/i18n';
import { useBlockProps, useInnerBlocksProps } from '@wordpress/block-editor';
import { _class } from '../components/_utils';

export default function JobListingPost({ attributes, setAttributes }) {
  const blockProps = useBlockProps({
    className: 'prose max-w-5xl pl-16'
  });

  const { children, ...innerBlocksProps } = useInnerBlocksProps(blockProps, {
    allowedBlocks: [
      'core/heading',
      'core/paragraph',
      'takt/button-group',
      'takt/button'
    ],
    templateLock: false,
    template: [
      [
        'core/heading',
        {
          placeholder: 'Title...'
        }
      ],
      [
        'core/paragraph',
        {
          placeholder: 'Description...'
        }
      ]
    ]
  });

  return (
    <div className="container container-wide py-20">
      <div {...innerBlocksProps}>{children}</div>;
    </div>
  );
}

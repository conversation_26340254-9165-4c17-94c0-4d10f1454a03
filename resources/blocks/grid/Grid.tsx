import { __ } from '@wordpress/i18n';

import {
  useBlockProps,
  useInnerBlocksProps,
  InspectorControls
} from '@wordpress/block-editor';

import BlockHeader from '../components/BlockHeader';
import { _class } from '../components/_utils';

import {
  PanelBody,
  __experimentalVStack as VStack,
  __experimentalToggleGroupControl as ToggleGroupControl,
  __experimentalToggleGroupControlOption as ToggleGroupControlOption
} from '@wordpress/components';

export default function Edit({ attributes, setAttributes }) {
  const { columns } = attributes;
  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    {
      className: _class({
        default: 'grid gap-4',
        'grid-cols-[repeat(3,_minmax(0,_1fr))]': columns == 3,
        'grid-cols-[repeat(4,_minmax(0,_1fr))]': columns == 4
      })
    },
    {
      allowedBlocks: [['takt/grid-contact'], ['takt/grid-text']]
    }
  );

  return (
    <>
      <InspectorControls>
        <PanelBody title="Grid Settings">
          <VStack spacing={1}>
            <ToggleGroupControl
              label="Columns"
              value={columns}
              isBlock
              __nextHasNoMarginBottom
              __next40pxDefaultSize
              onChange={(value) => {
                setAttributes({ columns: value });
              }}
            >
              <ToggleGroupControlOption value="3" label="3 Columns" />
              <ToggleGroupControlOption value="4" label="4 Columns" />
            </ToggleGroupControl>
          </VStack>
        </PanelBody>
      </InspectorControls>
      <div
        {...useBlockProps({
          className:
            'container relative flex flex-col gap-y-16 bg-gray px-16 py-16'
        })}
      >
        <div className="flex flex-col gap-y-16">
          <BlockHeader
            setAttributes={setAttributes}
            header={attributes.header}
          />
          <div {...innerBlocksProps}>{children}</div>
        </div>
      </div>
    </>
  );
}

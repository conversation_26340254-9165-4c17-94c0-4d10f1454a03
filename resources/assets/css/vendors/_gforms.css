.gform_wrapper {
  .gform_validation_errors {
    .gform_submission_error {
      margin-bottom: 0.5rem;
      font-size: var(--font-size-sm);

      &.hide_summary {
        @apply sr-only;
      }
    }
  }

  .gform_required_legend {
    @apply sr-only;
  }

  .gform_fields {
    --field-spacing: 1.5rem;

    display: flex;
    flex-wrap: wrap;
    gap: 24px var(--field-spacing);

    @media (min-width: theme('screens.xl')) {
      gap: 1.5rem var(--field-spacing);
    }

    .gfield {
      --field-width: 100%;

      flex: 1 0 var(--field-width);
      min-width: 16rem;

      &.gfield_visibility_hidden {
        display: none;
      }

      .ginput_container--name {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem var(--field-spacing);

        &.gf_name_has_2 {
          --field-width: calc(50% - var(--field-spacing));
        }

        .gform-grid-col {
          &.gform-grid-col--size-auto {
            flex: 1 0 var(--field-width);
          }
        }
      }

      :is(input:not([type='submit']), textarea, select) {
        width: 100%;
        background-color: theme('colors.white');
        padding: 12px;
        border: 1px solid theme('colors.gray');
        border-radius: 4px;

        &::placeholder {
          @apply transition-colors;

          color: theme('colors.white');
        }
        &:focus-within {
          outline: none;
          border-color: theme('colors.black');

          &::placeholder {
            color: theme('colors.white / 50%');
          }
        }
      }

      &.hidden_label {
        .gfield_label {
          @apply sr-only;
        }
      }

      &.gfield--width-half {
        --field-width: calc(50% - var(--field-spacing));
      }

      &.gfield--type-radio {
        :is(legend) {
          font-size: 1rem;
          letter-spacing: 0.05em;
          float: left;
          margin-right: 1.5rem;
        }

        .gfield_radio {
          display: flex;
          gap: 1rem;
        }

        .gchoice {
          display: flex;
          gap: 0.5rem;
          align-items: center;

          :is(input) {
            all: unset;
            background-color: transparent;
            border: 0;
            padding: 0;
            width: 1rem;
            height: 1rem;
            border: 1px solid theme('colors.white');
            border-radius: 9999px;

            &:checked {
              background-color: theme('colors.white');
            }
          }
        }
      }

      &.gfield--type-honeypot {
        @apply sr-only;
      }
    }
  }

  .gform_footer {
    margin-top: 1.5rem;

    input[type='submit'] {
      @apply btn btn-primary;
    }
  }

  .gfield_description {
    margin-top: 0.5rem;
    font-size: theme('fontSize.sm');

    &.validation_message {
      color: theme('colors.red.500');
    }
  }
  .gform_validation_errors {
    @apply sr-only;
  }
}

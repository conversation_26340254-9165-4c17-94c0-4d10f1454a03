declare var wpApiSettings: any;

import { __ } from '@wordpress/i18n';
import { useState, useEffect, memo } from '@wordpress/element';

import {
  RichText,
  useBlockProps,
  useInnerBlocksProps,
  InspectorControls,
  Image
} from '@wordpress/block-editor';

import {
  PanelBody,
  ToggleControl,
  __experimentalVStack as VStack,
  SelectControl,
  Spinner
} from '@wordpress/components';

import { _class } from '../components/_utils';

export default function MediaContent({ attributes, setAttributes }) {
  const { eyebrow, title, description, formId, image } = attributes;
  const [forms, setForms] = useState([]);
  const [formsLoading, setFormsLoading] = useState(true);

  const fetchGravityForms = async () => {
    const headers = new Headers();
    headers.append('X-WP-Nonce', wpApiSettings.nonce);

    try {
      const response = await fetch('/wp-json/gf/v2/forms', {
        method: 'GET',
        headers: headers,
        credentials: 'same-origin'
      });

      if (response.ok) {
        const data = await response.json();

        setForms(
          Object.values(data).map((form: any) => ({
            label: form.title,
            value: form.id
          }))
        );
        setFormsLoading(false);
      }
    } catch (error) {
      console.error('Fetch error:', error);
    }
  };

  useEffect(() => {
    fetchGravityForms();
  }, []);

  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    {
      className: 'flex gap-x-4'
    },
    {
      allowedBlocks: ['takt/button-group'],
      template: [['takt/button-group']]
    }
  );

  return (
    <div
      {...useBlockProps({
        className: _class({
          default:
            'container relative flex flex-col justify-between gap-y-12 px-16'
        })
      })}
    >
      <InspectorControls>
        <PanelBody title="Form Settings">
          <VStack>
            {!formsLoading ? (
              <SelectControl
                label="Select a form"
                value={formId}
                options={[{ label: 'Forms', value: 0 }, ...(forms ?? [])]}
                onChange={(value) => {
                  setAttributes({ formId: parseInt(value) });
                }}
              />
            ) : (
              <Spinner />
            )}
          </VStack>
        </PanelBody>
      </InspectorControls>
      <div className="flex w-full flex-col border-t border-current pt-3 laptop:flex-row">
        <div className="flex flex-col gap-y-8">
          <div className="flex items-center gap-3">
            <span className="h-5 w-5 rounded-full bg-teal"></span>
            <RichText
              tagName="p"
              className="text-base font-bold uppercase tracking-ultra"
              placeholder={__('Eyebrow text...', 'takt')}
              value={eyebrow}
              allowedFormats={[]}
              onChange={(value) => setAttributes({ eyebrow: value })}
              keepPlaceholderOnFocus
              disableLineBreaks={true}
            />
          </div>
        </div>
      </div>

      <div className="flex flex-col justify-between gap-x-20 gap-y-4 overflow-hidden tablet:grid tablet:grid-cols-2 tablet:gap-y-12">
        <div className="flex flex-col gap-y-8 tablet:col-[1]">
          <div className="flex gap-x-4">
            <RichText
              tagName="div"
              className={`text-3xl text-navy tablet:text-4xl`}
              placeholder={__('Title...', 'takt')}
              value={title}
              allowedFormats={[]}
              onChange={(value) => setAttributes({ title: value })}
              keepPlaceholderOnFocus
              disableLineBreaks={true}
            />
          </div>

          <div className="flex h-full flex-col gap-y-6">
            <RichText
              tagName="div"
              className={`text-base`}
              placeholder={__('Text comes here...', 'takt')}
              value={description}
              allowedFormats={['core/bold', 'core/italic', 'core/link']}
              onChange={(value) => setAttributes({ description: value })}
              keepPlaceholderOnFocus
              disableLineBreaks={true}
            />
          </div>

          <div {...innerBlocksProps}>{children}</div>
        </div>

        <div className="flex flex-1 shrink-0 tablet:col-[2]">
          {formId ? (
            <Form formId={formId} />
          ) : (
            <div className="flex h-full flex-col items-center justify-center gap-y-4">
              <span className="font-heading block text-4xl uppercase">
                No form selected
              </span>
              <p className="text-lg">
                Select a form from the dropdown on the right sidebar.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

const Form = memo(
  ({ formId, className = '' }: { formId?: string; className?: string }) => {
    const [formHTML, setFormHTML] = useState({ __html: '' });

    useEffect(() => {
      async function renderForm() {
        const response = await fetch(`/api/takt/v1/form/${formId}`, {
          method: 'GET'
        });
        const htmlString = await response.text();

        return {
          __html: htmlString
        };
      }

      if (formId) {
        renderForm().then((result) => setFormHTML(result));
      }
    }, [formId]);

    return (
      <div className={`${className}`}>
        <div className="gform" dangerouslySetInnerHTML={formHTML} />
      </div>
    );
  }
);

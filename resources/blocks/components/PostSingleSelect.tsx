import {
  ComboboxControl,
  __experimentalScrollable as Scrollable,
  TextControl,
  __experimentalVStack as VStack
} from '@wordpress/components';
import { useEntityRecords } from '@wordpress/core-data';
import { useEffect, useState } from '@wordpress/element';

type PostSingleSelectProps = {
  label?: string;
  value: number | null;
  postType?: string;
  onSelect?: (item: number) => void;
};

const PostSingleSelect: React.FC<PostSingleSelectProps> = ({
  label = 'Select Post',
  value,
  postType = 'post',
  onSelect = () => {}
}) => {
  const [availableItems, setAvailableItems] = useState([]);
  const [filteredItems, setFilteredItems] = useState([]);

  const { hasResolved, records }: any = useEntityRecords('postType', postType, {
    per_page: -1
  });

  useEffect(() => {
    if (!hasResolved) {
      return;
    }
    const items = records.map((item) => {
      return {
        value: item.id.toString(),
        label: item.title.rendered
      };
    });

    setAvailableItems(items);
    setFilteredItems(items);
  }, [hasResolved]);

  const setFilter = (value) => {
    setFilteredItems(
      availableItems.filter((item) =>
        label.toLowerCase().startsWith(value.toLowerCase())
      )
    );
  };

  return (
    <div>
      {hasResolved && (
        <ComboboxControl
          __next40pxDefaultSize
          __nextHasNoMarginBottom
          label={label}
          value={value?.toString()}
          onChange={(value) => onSelect(parseInt(value))}
          options={filteredItems}
          onFilterValueChange={setFilter}
        />
      )}
    </div>
  );
};

export default PostSingleSelect;

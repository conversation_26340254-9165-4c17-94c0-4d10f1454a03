@php
    $image = $block->attributes['image'] ?? [];
    $eyebrow = $block->attributes['eyebrow'] ?? '';
    $title = $block->attributes['title'] ?? '';
    $description = $block->attributes['description'] ?? '';
@endphp

<div
    class="flex flex-col justify-between gap-y-4 bg-white p-8 bite bite-x-1.5 bite-y-1 bite-5 md:gap-y-10 lg:gap-y-16"
>
    @if (! empty($image['url']))
        <figure class="relative">
            <img
                class="aspect-square size-12 justify-self-end object-cover md:size-16 lg:size-20"
                src="{{ $image['url'] }}"
                alt="{{ $image['alt'] ?? '' }}"
            />
        </figure>
    @endif

    <div class="flex flex-1 flex-col">
        <div class="flex flex-col gap-2">
            <p class="text-sm font-bold uppercase tracking-ultra text-black">
                {{ $eyebrow }}
            </p>
            <h4 class="text-lg font-bold leading-9 text-navy md:text-xl">
                {!! $title !!}
            </h4>
        </div>
        <div class="flex flex-col gap-y-4">
            <p
                class="text-black [&_a]:underline [&_a]:decoration-transparent [&_a]:transition-colors hover:[&_a]:decoration-current"
            >
                {!! $description !!}
            </p>
        </div>
    </div>
</div>

<?php

namespace App\PostTypes;

/**
 * Create Custom Post Type for Team Members
 */
add_action(
    'init',
    function () {
        $customArgs = [
            'menu_position' => 10,
            'supports' => [
                'editor',
                'title',
                'thumbnail',
                'revisions',
                'custom-fields',
                'excerpt'
            ],
            'has_archive' => false,
            'public' => true,
            'publicly_queryable' => false,
            'show_in_rest' => true,
            'rewrite' => [
                'slug' => 'team-member',
                'with_front' => false,
            ],
            'show_in_nav_menus' => true,
            'menu_icon' =>
            'data:image/svg+xml;base64,' .
                base64_encode(
                    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6"><path fill-rule="evenodd" d="M7.5 6a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM3.751 20.105a8.25 8.25 0 0 1 16.498 0 .75.75 0 0 1-.437.695A18.683 18.683 0 0 1 12 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 0 1-.437-.695Z" clip-rule="evenodd" /></svg>'
                ),
            'template_lock' => 'insert',
            'template' => [
                [
                    'takt/team-member-meta',
                    [
                        'lock' => [
                            'remove' => true,
                            'move' => true,
                        ],
                    ],
                ],
            ],
        ];

        \new_custom_post_type('team-member', 'Team Member', 'Team Members', $customArgs, 0);

        $taxonomies = [
            'position' => 'Positions',
        ];

        foreach ($taxonomies as $taxonomy => $label) {
            $args = [
                'hierarchical' => true,
                'labels' => [
                    'name' => $label,
                    'singular_name' => $label,
                    'search_items' => 'Search ' . $label,
                    'all_items' => 'All ' . $label,
                    'parent_item' => 'Parent ' . $label,
                    'parent_item_colon' => 'Parent ' . $label . ':',
                    'edit_item' => 'Edit ' . $label,
                    'update_item' => 'Update ' . $label,
                    'add_new_item' => 'Add New ' . $label,
                    'new_item_name' => 'New ' . $label,
                    'menu_name' => $label,
                ],
                'show_ui' => true,
                'show_in_rest' => true,
                'show_in_quick_edit' => true,
                'show_admin_column' => true,
                'rewrite' => [
                    'slug' => $taxonomy,
                    'with_front' => false,
                ],
                // Add the custom rest_base to avoid conflicts
                'rest_base' => $taxonomy . '_taxonomy',
            ];

            // Register each taxonomy
            register_taxonomy($taxonomy, ['team-member'], $args);
        };

        $postMeta = [
            'email',
            'location',
            'linkedin',
            'phone',
        ];

        foreach ($postMeta as $key => $customArgs) {
            $args = [
                'show_in_rest' => true,
                'single' => true,
                'type' => 'string',
                'sanitize_callback' => 'wp_kses_post',
            ];

            if (is_numeric($key)) {
                $key = $customArgs;
            }

            if (is_array($customArgs)) {
                $args = array_merge($args, $customArgs);
            }

            register_post_meta('team-member', $key, $args);
        }
    },
    0
);

add_filter('team-member', function ($title) {
    $screen = get_current_screen();

    if ($screen->post_type == 'team-member') {
        $title = 'Team Members';
    }

    return $title;
});

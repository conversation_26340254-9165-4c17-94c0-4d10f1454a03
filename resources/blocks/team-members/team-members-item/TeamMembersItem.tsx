import { __ } from '@wordpress/i18n';

import { use<PERSON>lockProps, InspectorControls } from '@wordpress/block-editor';
import { Spinner, PanelBody } from '@wordpress/components';
import {
  useEntityProp,
  useEntityRecord,
  store as coreStore
} from '@wordpress/core-data';
import { useSelect } from '@wordpress/data';

import { _class } from '../../components/_utils';
import PostSingleSelect from '../../components/PostSingleSelect';
import { Button } from '../../components';

export default function Edit({ attributes, setAttributes, context }) {
  const { teamMemberId } = attributes;

  const { record, hasResolved, status } = useEntityRecord(
    'postType',
    'team-member',
    teamMemberId
  );

  const Item = (
    <>
      {!teamMemberId ? (
        <div className="content-center justify-items-center bg-white px-12 py-8">
          <span className="text-xl">Please Select a Team Member</span>
        </div>
      ) : (
        <>
          {!hasResolved ? (
            <div className="content-center justify-items-center px-12 py-8">
              <Spinner />
            </div>
          ) : (
            <>
              {status === 'ERROR' ? (
                <div className="bg-off-white/20 content-center justify-items-center px-12 py-8">
                  <span className="text-xl">
                    That team member does not exist
                  </span>
                </div>
              ) : (
                <TeamMemberCard teamMemberId={teamMemberId} />
              )}
            </>
          )}
        </>
      )}
    </>
  );

  return (
    <>
      <InspectorControls>
        <PanelBody title="Team Member">
          <PostSingleSelect
            label="Select a Team Member"
            value={teamMemberId}
            postType="team-member"
            onSelect={(value) => {
              setAttributes({ teamMemberId: value });
            }}
          />
        </PanelBody>
      </InspectorControls>

      <div {...useBlockProps()}>{Item}</div>
    </>
  );
}

function TeamMemberCard({ teamMemberId }) {
  const [imageId, updateImageId] = useEntityProp(
    'postType',
    'team-member',
    'featured_media',
    teamMemberId
  );
  const image = useSelect(
    (select) => select(coreStore).getMedia(imageId),
    [imageId]
  );

  const [meta, updateMeta] = useEntityProp(
    'postType',
    'team-member',
    'meta',
    teamMemberId
  );

  const { record } = useEntityRecord('postType', 'team-member', teamMemberId);

  const {
    full_name,
    position,
    short_bio,
    email,
    location,
    linkedin,
    phone,
    featured_media,
    full_bio
  } = meta;

  return (
    <div className="has-circle flex">
      <div className="flex flex-1 flex-col gap-2">
        {image && image.source_url && (
          <img
            src={image.source_url}
            alt={record.title.rendered}
            className="aspect-square rounded bg-white object-cover object-top"
          />
        )}

        <div className="flex flex-col gap-4">
          <div className="flex flex-col">
            {full_name && (
              <div className="pt-4 text-base font-bold leading-7">
                {full_name}
              </div>
            )}

            {position && <div className="text-base">{position}</div>}
          </div>

          {short_bio && <div className="text-sm">{short_bio}</div>}
        </div>

        <div className="flex gap-2">
          {email && (
            <div className="flex flex-col gap-4">
              <a href={`mailto:${email}`}>
                <svg
                  width="24"
                  height="25"
                  viewBox="0 0 24 25"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="ic:baseline-email">
                    <path
                      id="Vector"
                      d="M20 4.78906H4C2.9 4.78906 2.01 5.68906 2.01 6.78906L2 18.7891C2 19.8891 2.9 20.7891 4 20.7891H20C21.1 20.7891 22 19.8891 22 18.7891V6.78906C22 5.68906 21.1 4.78906 20 4.78906ZM20 8.78906L12 13.7891L4 8.78906V6.78906L12 11.7891L20 6.78906V8.78906Z"
                      fill="#004D86"
                    />
                  </g>
                </svg>
              </a>
            </div>
          )}

          {linkedin && (
            <a href={linkedin}>
              <svg
                width="24"
                height="25"
                viewBox="0 0 24 25"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="Icon / LinkedIn">
                  <path
                    id="Vector"
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M4.5 4.03174C3.67157 4.03174 3 4.70331 3 5.53174V20.5317C3 21.3601 3.67157 22.0317 4.5 22.0317H19.5C20.3284 22.0317 21 21.3601 21 20.5317V5.53174C21 4.70331 20.3284 4.03174 19.5 4.03174H4.5ZM8.52076 8.03446C8.52639 8.99071 7.81061 9.57993 6.96123 9.57571C6.16107 9.57149 5.46357 8.93446 5.46779 8.03587C5.47201 7.19071 6.13998 6.51149 7.00764 6.53118C7.88795 6.55087 8.52639 7.19634 8.52076 8.03446ZM12.2797 10.7935H9.75971H9.7583V19.3533H12.4217V19.1536C12.4217 18.7737 12.4214 18.3937 12.4211 18.0136C12.4203 16.9998 12.4194 15.9849 12.4246 14.9714C12.426 14.7253 12.4372 14.4694 12.5005 14.2345C12.7381 13.357 13.5271 12.7903 14.4074 12.9296C14.9727 13.0181 15.3467 13.3458 15.5042 13.8788C15.6013 14.212 15.6449 14.5706 15.6491 14.918C15.6605 15.9656 15.6589 17.0132 15.6573 18.0609C15.6567 18.4307 15.6561 18.8007 15.6561 19.1705V19.3519H18.328V19.1466C18.328 18.6946 18.3278 18.2427 18.3275 17.7908C18.327 16.6613 18.3264 15.5318 18.3294 14.4019C18.3308 13.8914 18.276 13.388 18.1508 12.8944C17.9638 12.1603 17.5771 11.5528 16.9485 11.1141C16.5027 10.8019 16.0133 10.6008 15.4663 10.5783C15.404 10.5757 15.3412 10.5724 15.2781 10.5689C14.9984 10.5538 14.7141 10.5385 14.4467 10.5924C13.6817 10.7457 13.0096 11.0958 12.5019 11.7131C12.4429 11.7839 12.3852 11.8558 12.2991 11.9631L12.2797 11.9874V10.7935ZM5.68164 19.3561H8.33242V10.7991H5.68164V19.3561Z"
                    fill="#004D86"
                  />
                </g>
              </svg>
            </a>
          )}
        </div>
      </div>
    </div>
  );
}

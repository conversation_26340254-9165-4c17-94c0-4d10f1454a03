<x-block :$block class="container py-28">
    <div
        class="flex w-full flex-col gap-y-20"
        x-data="news(@js($perPageOptions))"
    >
        <div
            class="news-listings flex w-full grid-cols-4 flex-wrap items-center justify-between gap-x-4 gap-y-6 tablet:grid tablet:items-start tablet:justify-start tablet:gap-x-16"
        >
            <div
                x-data="{ open: false }"
                class="relative col-span-1 row-start-1 row-end-3 tablet:w-full"
            >
                <div
                    class="fixed left-0 top-0 z-[998] h-screen w-screen bg-black/30 transition-opacity duration-300"
                    :class="open ? 'opacity-100 ' : 'opacity-0 pointer-events-none'"
                    @click="open = false"
                ></div>
                <!-- Mobile Filters Button -->
                <button
                    class="relative rounded-full bg-white px-8 py-4 shadow-xl shadow-black/5 laptop:hidden"
                    @click="open = true"
                >
                    <span class="text-sm font-bold">Filters</span>
                    <svg
                        class="ml-2 inline-block h-4 w-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"
                        />
                    </svg>
                </button>

                <!-- Filters Panel -->
                <div
                    class="filters-panel fixed left-0 top-0 z-[999] h-screen w-full overflow-y-auto bg-white p-6 transition-transform duration-300 laptop:relative laptop:h-auto laptop:w-auto laptop:transform-none laptop:bg-transparent laptop:p-0"
                    :class="open ? 'translate-x-0' : '-translate-x-full laptop:translate-x-0'"
                >
                    <!-- Close button for mobile -->
                    <button
                        class="absolute right-4 top-4 laptop:hidden"
                        @click="open = false"
                    >
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>

                    <div class="space-y-6">
                        <!-- Search -->
                        <div>
                            <label class="block text-sm font-bold mb-2">Search</label>
                            <input
                                type="text"
                                x-model="searchQuery"
                                @input.debounce.500ms="applyFilters()"
                                placeholder="Search news..."
                                class="w-full rounded border border-gray-300 px-3 py-2 focus:border-teal focus:outline-none"
                            />
                        </div>

                        <!-- Category Filter -->
                        <div x-data="{ open: false }">
                            <button
                                @click="open = !open"
                                class="flex w-full items-center justify-between rounded border border-gray-300 px-3 py-2 text-left"
                            >
                                <span class="text-sm font-bold">Category</span>
                                <svg
                                    class="h-4 w-4 transition-transform"
                                    :class="open ? 'rotate-180' : ''"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div
                                x-show="open"
                                x-collapse
                                class="accordion-content scrollbar mb-4 flex max-h-[450px] flex-col space-y-4 overflow-y-auto"
                            >
                                <select
                                    class="ml-1 cursor-pointer rounded-none border-b border-black bg-transparent px-0 py-3 transition-opacity duration-300 hover:opacity-70 focus:outline-none"
                                    x-model="selectedFilters['category']"
                                    @change="applyFilters()"
                                >
                                    <option value="">All Categories</option>
                                    <template x-for="category in categories" :key="category.id">
                                        <option :value="category.slug" x-text="category.name"></option>
                                    </template>
                                </select>
                            </div>
                        </div>

                        <!-- Sort -->
                        <div>
                            <label class="block text-sm font-bold mb-2">Sort By</label>
                            <select
                                x-model="selectedFilters.sort"
                                @change="applyFilters()"
                                class="w-full rounded border border-gray-300 px-3 py-2 focus:border-teal focus:outline-none"
                            >
                                <option value="date">Newest First</option>
                                <option value="date_asc">Oldest First</option>
                                <option value="title">Title A-Z</option>
                                <option value="title_desc">Title Z-A</option>
                            </select>
                        </div>

                        <!-- Clear Filters -->
                        <button
                            @click="clearFilters()"
                            class="w-full rounded bg-teal px-4 py-2 text-white hover:bg-teal-dark"
                        >
                            Clear All Filters
                        </button>
                    </div>
                </div>
            </div>

            <!-- Results Info -->
            <div class="col-span-4 laptop:col-span-3">
                <div class="mb-6 flex items-center justify-between">
                    <p class="text-sm text-gray-600">
                        <span x-text="total_count"></span> news articles found
                    </p>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm">Show:</label>
                        <select
                            x-model="perPage"
                            @change="applyFilters()"
                            class="rounded border border-gray-300 px-2 py-1 text-sm"
                        >
                            <template x-for="option in @js($perPageOptions)" :key="option">
                                <option :value="option" x-text="option"></option>
                            </template>
                        </select>
                    </div>
                </div>
            </div>

            <div
                class="news col-span-4 grid w-full flex-none auto-rows-max grid-cols-4 gap-4 tabletLg:grid-cols-3 laptop:col-span-3"
                x-init="applyFilters()"
            >
                <div
                    x-show="!posts || posts.length === 0"
                    class="col-span-4 mt-20 py-4 text-center"
                >
                    <span>Loading news...</span>
                </div>
                <div
                    x-show="posts && posts.length > 0 && (! filteredPosts || filteredPosts.length === 0)"
                    class="col-span-4 mt-20 py-4 text-center"
                >
                    <span>No news articles match your criteria.</span>
                    <button
                        @click="clearFilters()"
                        class="mt-4 rounded bg-teal px-4 py-2 text-white"
                    >
                        Clear Filters
                    </button>
                </div>
                <template x-for="(post, index) in filteredPosts" :key="index">
                    <div
                        class="news-card col-span-4 flex flex-col justify-between overflow-clip rounded border border-navy mobile:col-span-2 tabletLg:col-span-1"
                    >
                        <template x-if="post.featured_image">
                            <figure class="aspect-video w-full overflow-hidden">
                                <img
                                    class="h-full w-full object-cover"
                                    :src="post.featured_image"
                                    :alt="post.title"
                                />
                            </figure>
                        </template>

                        <div class="flex flex-1 flex-col justify-between space-y-2 p-3">
                            <div>
                                <h5 class="text-sm font-bold line-clamp-2">
                                    <a :href="post.link" x-html="post.title"></a>
                                </h5>

                                <template x-if="post.excerpt">
                                    <p class="mt-2 text-xs text-gray-600 line-clamp-3" x-html="post.excerpt"></p>
                                </template>

                                <template x-if="post.categories && post.categories.length > 0">
                                    <div class="mt-2 flex flex-wrap gap-1">
                                        <template x-for="category in post.categories.slice(0, 2)" :key="category.id">
                                            <span class="rounded bg-gray-100 px-2 py-1 text-xs" x-text="category.name"></span>
                                        </template>
                                    </div>
                                </template>
                            </div>

                            <div class="mt-4 flex items-center justify-between text-xs text-gray-500">
                                <span x-text="post.date"></span>
                                <a :href="post.link" class="text-teal hover:underline">Read More</a>
                            </div>
                        </div>
                    </div>
                </template>
            </div>

            <!-- Pagination -->
            <div
                x-show="totalPages > 1"
                class="col-span-4 mt-8 flex items-center justify-center space-x-2"
            >
                <button
                    @click="prevPage()"
                    :disabled="page <= 1"
                    class="rounded bg-gray-200 px-3 py-2 text-sm disabled:opacity-50"
                >
                    Previous
                </button>
                <span class="text-sm">
                    Page <span x-text="page"></span> of <span x-text="totalPages"></span>
                </span>
                <button
                    @click="nextPage()"
                    :disabled="page >= totalPages"
                    class="rounded bg-gray-200 px-3 py-2 text-sm disabled:opacity-50"
                >
                    Next
                </button>
            </div>
        </div>
    </div>
</x-block>

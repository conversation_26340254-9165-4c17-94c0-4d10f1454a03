import { __ } from '@wordpress/i18n';

import {
  use<PERSON>lockProps,
  RichText,
  useInnerBlocksProps,
  InspectorControls
} from '@wordpress/block-editor';

import {
  PanelBody,
  __experimentalHeading as Heading,
  __experimentalVStack as VStack,
  __experimentalToggleGroupControl as ToggleGroupControl,
  __experimentalToggleGroupControlOption as ToggleGroupControlOption
} from '@wordpress/components';

import { _class } from '../components/_utils';
import PlaceholderImage from '../components/PlaceholderImage';

export default function Cta({ attributes, setAttributes }) {
  const { title, description, image, mediaPosition } = attributes;

  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    { className: 'flex gap-x-4 mt-auto' },
    {
      allowedBlocks: ['takt/button-group', 'takt/button'],
      template: [['takt/button-group']],
      templateLock: false
    }
  );

  return (
    <>
      <InspectorControls>
        <PanelBody title="Media Position">
          <ToggleGroupControl
            label="Media Position"
            value={mediaPosition}
            onChange={(value) => setAttributes({ mediaPosition: value })}
            isBlock
            __nextHasNoMarginBottom
            __next40pxDefaultSize
          >
            <ToggleGroupControlOption value="left" label="Left" />
            <ToggleGroupControlOption value="right" label="Right" />
          </ToggleGroupControl>
        </PanelBody>
      </InspectorControls>

      <div
        {...useBlockProps({
          className: 'container'
        })}
      >
        <div
          className={_class({
            default: 'grid overflow-hidden rounded bg-gray',
            'grid-cols-[minmax(0,_7fr)_minmax(0,_3fr)]':
              mediaPosition === 'right',
            'grid-cols-[minmax(0,_3fr)_minmax(0,_7fr)]':
              mediaPosition === 'left'
          })}
        >
          <div
            className={_class({
              default: 'flex flex-col gap-y-8 p-16',
              'col-[1] row-[1]': mediaPosition === 'right',
              'col-[2] row-[1]': mediaPosition === 'left'
            })}
          >
            <div className="flex shrink-0 gap-x-4">
              <RichText
                tagName="div"
                className={`text-3xl text-navy tablet:text-4xl`}
                placeholder={__('Title...', 'takt')}
                value={title}
                allowedFormats={[]}
                onChange={(value) => setAttributes({ title: value })}
                keepPlaceholderOnFocus
                disableLineBreaks={true}
              />
            </div>

            <div className="flex flex-col gap-y-6">
              <RichText
                tagName="div"
                className={`text-base`}
                placeholder={__('Text comes here...', 'takt')}
                value={description}
                allowedFormats={['core/bold', 'core/italic', 'core/link']}
                onChange={(value) => setAttributes({ description: value })}
                keepPlaceholderOnFocus
                disableLineBreaks={true}
              />
            </div>

            <div {...innerBlocksProps}>{children}</div>
          </div>

          <div className="h-full">
            <PlaceholderImage
              image={image}
              className="h-full w-full"
              placeholderClass="w-full h-full"
              imageClass="w-full h-full object-cover"
              onSelect={(media) => {
                setAttributes({
                  image: {
                    id: media.id,
                    url: media.url,
                    alt: media.alt
                  }
                });
              }}
              onDelete={() => {
                setAttributes({
                  image: {
                    id: null,
                    url: null,
                    alt: ''
                  }
                });
              }}
            />
          </div>
        </div>
      </div>
    </>
  );
}

import { __ } from '@wordpress/i18n';

import {
  RichText,
  useBlockProps,
  useInnerBlocksProps,
  InspectorControls,
  MediaUpload
} from '@wordpress/block-editor';

import {
  PanelBody,
  Button,
  ToggleControl,
  __experimentalHeading as Heading,
  __experimentalVStack as VStack,
  __experimentalToggleGroupControl as ToggleGroupControl,
  __experimentalToggleGroupControlOption as ToggleGroupControlOption,
  TextControl
} from '@wordpress/components';

import { _class } from '../components/_utils';
import useEmbedUrl from '../components/_use-embed-url';
import { Content_Blocks } from '../components';
import radialBgImage from './radialBgImage.png';

export default function MediaContent({ attributes, setAttributes }) {
  const {
    eyebrow,
    title,
    description,
    image,
    layout,
    mediaPosition,
    mediaType,
    videoSource,
    video,
    videoUrl,
    hasBgGraphic
  } = attributes;

  const { embedUrl, videoId } = useEmbedUrl(videoUrl, videoSource);

  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    {
      className: 'flex flex-col prose'
    },
    {
      allowedBlocks: [...Content_Blocks],
      template: [['core/paragraph', { placeholder: 'Content goes here...' }]]
    }
  );

  return (
    <>
      <InspectorControls>
        <PanelBody title="Media Content Settings">
          <VStack spacing={1}>
            <ToggleGroupControl
              label="Space Distribution"
              value={layout}
              isBlock
              __nextHasNoMarginBottom
              __next40pxDefaultSize
              onChange={(value) => setAttributes({ layout: value })}
            >
              <ToggleGroupControlOption value="fifty-fifty" label="Equal" />
              <ToggleGroupControlOption
                value="thirty-seventy"
                label="Narrow Image"
              />
            </ToggleGroupControl>

            <ToggleGroupControl
              label="Background Graphic"
              value={hasBgGraphic ? 'true' : 'false'}
              onChange={(value) =>
                setAttributes({ hasBgGraphic: value === 'true' })
              }
              isBlock
              __nextHasNoMarginBottom
              __next40pxDefaultSize
            >
              <ToggleGroupControlOption value="true" label="Yes" />
              <ToggleGroupControlOption value="false" label="No" />
            </ToggleGroupControl>

            <ToggleGroupControl
              label="Media Position"
              value={mediaPosition}
              onChange={(value) => setAttributes({ mediaPosition: value })}
              isBlock
              __nextHasNoMarginBottom
              __next40pxDefaultSize
            >
              <ToggleGroupControlOption value="left" label="Left" />
              <ToggleGroupControlOption value="right" label="Right" />
            </ToggleGroupControl>

            <ToggleGroupControl
              label="Media Type"
              value={mediaType}
              isBlock
              __nextHasNoMarginBottom
              __next40pxDefaultSize
              onChange={(value) => {
                setAttributes({ mediaType: value });
              }}
            >
              <ToggleGroupControlOption value="image" label="Image" />
              <ToggleGroupControlOption value="video" label="Video" />
            </ToggleGroupControl>

            {mediaType === 'video' && (
              <>
                <ToggleGroupControl
                  label="Video Source"
                  value={videoSource}
                  isBlock
                  __nextHasNoMarginBottom
                  __next40pxDefaultSize
                  onChange={(value) => {
                    setAttributes({ videoSource: value });
                  }}
                >
                  <ToggleGroupControlOption value="local" label="Upload" />
                  <ToggleGroupControlOption value="youtube" label="YouTube" />
                  <ToggleGroupControlOption value="vimeo" label="Vimeo" />
                </ToggleGroupControl>

                {videoSource !== 'local' ? (
                  <TextControl
                    label="Video URL"
                    value={videoUrl}
                    onChange={(value) => {
                      setAttributes({ videoUrl: value });
                    }}
                  />
                ) : (
                  <MediaUpload
                    onSelect={(media) => {
                      setAttributes({
                        video: {
                          id: media.id,
                          src: media.url,
                          mime: media.mime
                        }
                      });
                    }}
                    allowedTypes={['video']}
                    value={video.id}
                    render={({ open }) => (
                      <>
                        <Heading className="mb-0">Video</Heading>
                        <div className="mb-4 flex gap-2">
                          <Button variant="primary" onClick={open}>
                            {!!video.src ? 'Replace Video' : 'Set Video'}
                          </Button>
                          {!!video.src && (
                            <Button
                              variant="secondary"
                              onClick={() => {
                                setAttributes({
                                  video: {
                                    id: null,
                                    src: null
                                  }
                                });
                              }}
                            >
                              Reset Video
                            </Button>
                          )}
                        </div>
                      </>
                    )}
                  />
                )}
              </>
            )}
          </VStack>
        </PanelBody>
      </InspectorControls>

      <div
        {...useBlockProps({
          className: _class({
            default:
              'container relative z-20 flex min-h-[713px] flex-col justify-between gap-y-12 px-16'
          })
        })}
      >
        {hasBgGraphic && (
          <div
            className="absolute bottom-0 right-0 z-10 h-full w-full bg-contain bg-no-repeat"
            style={{
              backgroundImage: `url(${radialBgImage})`,
              backgroundSize: 'contain',
              backgroundPosition: 'right',
              opacity: 0.5
            }}
          ></div>
        )}
        <div className="flex w-full flex-col border-t border-current pt-3 laptop:flex-row">
          <div className="flex flex-col gap-y-8">
            <div className="flex items-center gap-3">
              <span className="h-5 w-5 rounded-full bg-teal"></span>
              <RichText
                tagName="p"
                className="text-base font-bold uppercase tracking-ultra"
                placeholder={__('Eyebrow text...', 'takt')}
                value={eyebrow}
                allowedFormats={[]}
                onChange={(value) => setAttributes({ eyebrow: value })}
                keepPlaceholderOnFocus
                disableLineBreaks={true}
              />
            </div>
          </div>
        </div>

        <div
          className={_class({
            default: 'container relative z-20 overflow-hidden'
          })}
        >
          <div
            className={_class({
              default: 'grid grid-cols-10 gap-x-8 gap-y-12'
            })}
          >
            <div
              className={_class({
                default: 'grid grid-rows-[max-content] gap-y-4',
                'col-span-5': layout === 'fifty-fifty',
                'col-span-7': layout !== 'fifty-fifty'
              })}
            >
              <div className="flex shrink-0 gap-x-4">
                <RichText
                  tagName="div"
                  className={`text-3xl text-navy tablet:text-4xl`}
                  placeholder={__('Title...', 'takt')}
                  value={title}
                  allowedFormats={[]}
                  onChange={(value) => setAttributes({ title: value })}
                  keepPlaceholderOnFocus
                />
              </div>

              <div {...innerBlocksProps}>{children}</div>
            </div>

            {mediaType === 'image' ? (
              <div
                className={_class({
                  default: 'min-h-full',
                  'col-span-5': layout === 'fifty-fifty',
                  'col-span-3': layout !== 'fifty-fifty',
                  'order-first': mediaPosition === 'left'
                })}
              >
                <figure className="relative h-full">
                  <img
                    className="has-circle top-0 h-full w-full object-cover"
                    src={image.url}
                    alt={image.alt}
                    style={{
                      objectPosition: image.imagePosition ?? 'center center'
                    }}
                  />
                </figure>
              </div>
            ) : (
              <>
                {videoSource === 'local' ? (
                  <div
                    className={_class({
                      default: 'has-circle min-h-full overflow-hidden',
                      'col-[1] row-[1] min-h-full': mediaPosition === 'left',
                      'col-[2] row-[1] min-h-full': mediaPosition === 'right'
                    })}
                  >
                    <video
                      className="has-circle min-h-full w-full object-cover"
                      autoPlay
                      loop
                      muted
                      key={video.id}
                    >
                      <source src={video.src} type={video.mime ?? ''} />
                    </video>
                  </div>
                ) : (
                  embedUrl && (
                    <div
                      className={_class({
                        default: 'has-circle min-h-full overflow-hidden',
                        'col-[1] row-[1] min-h-full': mediaPosition === 'left',
                        'col-[2] row-[1] min-h-full': mediaPosition === 'right'
                      })}
                    >
                      <iframe
                        src={`${embedUrl}`}
                        className="min-h-full min-w-full object-cover"
                        allow="autoplay; fullscreen"
                        allowFullScreen
                      ></iframe>
                    </div>
                  )
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

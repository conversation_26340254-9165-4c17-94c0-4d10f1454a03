{"name": "rslee-starter", "private": true, "browserslist": ["extends @wordpress/browserslist-config"], "engines": {"node": ">=16.0.0"}, "scripts": {"dev": "vite", "build": "vite build && bun run blocks:build", "blocks": "wp-scripts start --webpack-src-dir=./resources/blocks/ --output-path=./dist/blocks", "blocks:build": "wp-scripts build --webpack-src-dir=./resources/blocks/ --output-path=./dist/blocks"}, "devDependencies": {"@kucrut/vite-for-wp": "^0.10.0", "@prettier/plugin-php": "^0.22.4", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@vitejs/plugin-react": "^4.4.0", "@wordpress/block-editor": "^13.4.0", "@wordpress/browserslist-config": "^5.41.0", "@wordpress/components": "^27.6.0", "@wordpress/compose": "^7.22.0", "@wordpress/core-data": "^7.22.0", "@wordpress/create-block": "4.39.0", "@wordpress/data": "^10.22.0", "@wordpress/edit-post": "^8.22.0", "@wordpress/edit-site": "^6.22.0", "@wordpress/editor": "^14.22.0", "@wordpress/hooks": "^4.22.0", "@wordpress/i18n": "^5.22.0", "@wordpress/scripts": "^27.9.0", "postcss-easy-import": "^4.0.0", "prettier": "^3.5.3", "prettier-plugin-blade": "^2.1.21", "prettier-plugin-tailwindcss": "^0.6.11", "ts-loader": "^9.5.2", "vite": "^6.3.1"}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "@alpinejs/collapse": "^3.14.9", "@alpinejs/focus": "^3.14.9", "@alpinejs/intersect": "^3.14.9", "@alpinejs/resize": "^3.14.9", "alpinejs": "^3.14.9", "autoprefixer": "latest", "gsap": "^3.12.7", "postcss": "latest", "rollup-plugin-external-globals": "^0.13.0", "swiper": "^11.2.6", "tailwindcss": "^3.4.17", "vite-plugin-external": "^4.3.1"}}
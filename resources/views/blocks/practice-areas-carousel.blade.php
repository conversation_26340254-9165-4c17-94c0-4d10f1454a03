<x-block :$block class="container">
  <div>
    <x-block-header :header="$header" class="pb-16" />

    @php
    $args = [
    'post_type' => 'practice-area',
    'posts_per_page' => -1,
    'post_status' => 'publish',
    'orderBy' => 'title',
    'order' => 'ASC',
    ];
    $practice_areas = new WP_Query($args);
    @endphp

    @if ($practice_areas->have_posts())

      <div class="relative">
        @if ($attributes['enableNavigation'])
          <div class="swiper-navigation flex justify-between absolute inset-0">
            <div class="swiper-button-prev absolute top-[50%] bg-navy rounded p-3 translate-x-[-50%] translate-y-[calc(-50%-12px)] z-10 left-2 tablet:left-0 cursor-pointer">
              <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="Icon">
                  <path id="Vector" d="M20 11.2109H7.83L13.42 5.62094L12 4.21094L4 12.2109L12 20.2109L13.41 18.8009L7.83 13.2109H20V11.2109Z" fill="white" />
                </g>
              </svg>
            </div>
            <div class="swiper-button-next absolute top-[50%] bg-navy rounded p-3 translate-x-[50%] translate-y-[calc(-50%-12px)] z-10 right-2 tablet:right-0 cursor-pointer">
              <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="Icon">
                  <path id="Vector" d="M12 4.21094L10.59 5.62094L16.17 11.2109H4V13.2109H16.17L10.59 18.8009L12 20.2109L20 12.2109L12 4.21094Z" fill="white" />
                </g>
              </svg>
            </div>
          </div>
        @endif

        <div
          x-data="slider({
            loop: false,
            slidesPerView: 1,
            speed: 300,
            allowTouchMove: true,
            breakpoints: {
              1024: {
                slidesPerView: 3,
                spaceBetween: 30,
                allowTouchMove: false,
              },
              768: {
                slidesPerView: 2,
                spaceBetween: 20,
                allowTouchMove: true,
              },
              640: {
                slidesPerView: 1,
                spaceBetween: 10,
                allowTouchMove: true,
              },
            },
            navigation: {
              nextEl: '.swiper-button-next',
              prevEl: '.swiper-button-prev'
            }
          })"
          x-init="
            $nextTick(() => {
                const wrapper = $el.querySelector('.swiper-wrapper')
                const slides = [...$el.querySelectorAll('.swiper-slide')]

                wrapper.innerHTML = ''

                slides.sort(() => Math.random() - 0.5)
                slides.forEach((slide) => wrapper.appendChild(slide))

                const duplicates = slides.map((slide) => slide.cloneNode(true))
                duplicates.forEach((slide) => wrapper.appendChild(slide))
                swiper.update()

                const totalSlides = swiper.slides.length;
                console.log(totalSlides);
                const currentSlide = document.querySelectorAll('.slideNumber');
                currentSlide.forEach((slide) => {
                  slide.innerHTML = `/${totalSlides}`;
                })
            })
        ">
          <div class="swiper">
            <div class="swiper-wrapper ease-linear [counter-reset:item]">

              @while ($practice_areas->have_posts())
              @php
              $practice_areas->the_post();
              $description = get_post_meta(get_the_ID(), 'description', true);
              $subtitle = get_post_meta(get_the_ID(), 'subtitle', true);
              $content = get_the_content();
              $post_name = get_post_field('post_name', get_post());
              $page_permalink = get_page_link($attributes['pageId']);
              @endphp
                <div class="swiper-slide w-full h-[340px] laptop:w-1/3 flex flex-col">
                  <div class="practiceAreaCarousel p-6 relative rounded overflow-hidden border border-navy hover:border-teal transition-all duration-300 [counter-increment:item] h-[340px] flex">
                    @if ($attributes['linksDestination'] === 'external-page')
                    <a href="{{ $page_permalink }}#{{ $post_name }}" class="w-full flex flex-col gap-y-2 no-underline">
                      @else
                      <a href="#{{ $post_name }}" class="w-full flex flex-col gap-y-2 no-underline">
                        @endif
                        <div class="gradient">
                          <div class="image-wrapper">
                            @if (has_post_thumbnail())
                            {{ the_post_thumbnail('medium') }}
                            @endif
                          </div>
                        </div>
                        <div class="flex w-full h-full justify-between">
                          <h2 class="relative h-auto mt-auto w-full text-xl text-navy font-bold capitalize leading-9 pointer-events-none">
                            {!! get_the_title() !!}
                          </h2>
                          <svg class="z-[1] pointer-events-none" width="54" height="54" viewBox="0 0 54 54" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1.59082 52.4094L51.7757 2.22447M51.7757 2.22447L51.3018 49.1474M51.7757 2.22447L4.85284 2.69843" stroke="#004D86" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
                          </svg>
                        </div>
                      </a>
                  </div>
                  @if ($attributes['enablePagination'])
                    <span aria-hidden="true" class="slideNumber flex before:block before:content-[counter(item)] mt-2"></span>
                  @endif
                </div>
              @endwhile
            </div>

            @php wp_reset_postdata(); @endphp
          </div>
        </div>
      </div>

    @endif
  </div>
</x-block>

import { __ } from '@wordpress/i18n';

import {
  RichText,
  useBlockProps,
  useInnerBlocksProps
} from '@wordpress/block-editor';

import { _class } from '../components/_utils';

export default function SplitText({ attributes, setAttributes }) {
  const { eyebrow, title, description } = attributes;

  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    {
      className: 'flex gap-x-4'
    },
    {
      allowedBlocks: ['takt/button-group'],
      template: [['takt/button-group']]
    }
  );

  return (
    <div
      {...useBlockProps({
        className: _class({
          default:
            'container relative flex min-h-[713px] flex-col gap-y-12 px-16'
        })
      })}
    >
      <div className="flex w-full flex-col border-t border-current pt-3 laptop:flex-row">
        <div className="flex flex-col gap-y-8">
          <div className="flex items-center gap-3">
            <span className="h-5 w-5 rounded-full bg-teal"></span>
            <RichText
              tagName="p"
              className="text-base font-bold uppercase tracking-ultra"
              placeholder={__('Eyebrow text...', 'takt')}
              value={eyebrow}
              allowedFormats={[]}
              onChange={(value) => setAttributes({ eyebrow: value })}
              keepPlaceholderOnFocus
              disableLineBreaks={true}
            />
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-y-4 laptop:flex-row">
        <div className="flex w-[412px] shrink-0 gap-x-4">
          <RichText
            tagName="div"
            className={`text-3xl text-navy tablet:text-4xl`}
            placeholder={__('Title...', 'takt')}
            value={title}
            allowedFormats={[]}
            onChange={(value) => setAttributes({ title: value })}
            keepPlaceholderOnFocus
            disableLineBreaks={true}
          />
        </div>

        <div className="flex min-w-[300px] flex-col gap-y-6">
          <RichText
            tagName="div"
            className={`text-base`}
            placeholder={__('Text comes here...', 'takt')}
            value={description}
            allowedFormats={['core/bold', 'core/italic', 'core/link']}
            onChange={(value) => setAttributes({ description: value })}
            keepPlaceholderOnFocus
          />
          <div {...innerBlocksProps}>{children}</div>
        </div>
      </div>
    </div>
  );
}

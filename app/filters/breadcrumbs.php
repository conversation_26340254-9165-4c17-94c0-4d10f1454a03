<?php

namespace App\Filters;

use Illuminate\Support\Arr;

// add_filter('wpseo_breadcrumb_single_link', function ($link_output) {
//     dump($link_output);
//     if (is_singular('job-listing')) {
//         return '';
//     }

//     return $link_output;
// });

add_filter('wpseo_breadcrumb_links', function ($breadcrumbs) {

    $last = end($breadcrumbs);

    if (Arr::has($last, 'id') && get_post_type($last['id']) === 'job-listing') {
        $breadcrumbs = array_slice($breadcrumbs, 0, -1);
    }

    return $breadcrumbs;
});

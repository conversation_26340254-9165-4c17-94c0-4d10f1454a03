const variations = [
  {
    name: 'primary',
    title: 'Primary Button',
    scope: ['transform'],
    attributes: {
      type: 'default'
    },
    isActive: ['type'],
    isDefault: true
  },
  {
    title: 'Inverted Button',
    name: 'inverted',
    scope: ['transform'],
    attributes: {
      type: 'inverted'
    },
    isActive: ['type']
  },
  {
    name: 'secondary',
    title: 'Secondary Button',
    scope: ['transform'],
    attributes: {
      type: 'secondary'
    },
    isActive: ['type']
  },
  {
    name: 'tertiary',
    title: 'Tertiary Button',
    scope: ['transform'],
    attributes: {
      type: 'tertiary'
    },
    isActive: ['type']
  }
];

export default variations;

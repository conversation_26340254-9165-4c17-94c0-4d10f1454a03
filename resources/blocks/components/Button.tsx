import { __ } from '@wordpress/i18n';

import {
  RichText,
  __experimentalLinkControl as LinkControl
} from '@wordpress/block-editor';
import {
  Popover,
  __experimentalVStack as VStack,
  __experimentalToggleGroupControl as ToggleGroupControl,
  __experimentalToggleGroupControlOption as ToggleGroupControlOption
} from '@wordpress/components';
import { store } from '@wordpress/core-data';
import { useDispatch } from '@wordpress/data';
import { render, useRef } from '@wordpress/element';
import { decodeEntities } from '@wordpress/html-entities';

import { _class } from './_utils';

export type ButtonVariationTypes = 'primary' | 'secondary';

type ButtonProps = {
  children?: string;
  link?: {
    title: string;
    url: string;
    opensInNewTab?: boolean;
  };
  className?: string;
  onChange?: (value: {
    title?: string;
    url?: string;
    opensInNewTab?: boolean;
  }) => void;
  onClick?: () => void;
  onClose?: () => void;
  onLinkRemove?: () => void;
  showLinkBox?: boolean;
  placeholder?: string;
  anchor?: HTMLElement;
  focusOnMount?: string | boolean;
  variation?: ButtonVariationTypes;
  onVariationSelect?: (value: ButtonVariationTypes) => void;
};

export default function Button({
  children = '',
  link = {
    title: '',
    url: '',
    opensInNewTab: false
  },
  className = '',
  onChange,
  onClick = () => {},
  onClose = () => {},
  onLinkRemove = () => {},
  showLinkBox = false,
  placeholder = 'Button Label',
  anchor,
  variation = 'primary',
  onVariationSelect
}: ButtonProps) {
  const buttonRef = useRef(null);

  return (
    <div
      ref={buttonRef}
      onClick={onClick}
      className={_class({
        default: `btn not-prose relative transition-colors ${className}`,
        'btn-primary': variation === 'primary',
        'btn-secondary': variation === 'secondary',
        'btn-tertiary': variation === 'tertiary',
        'btn-inverted': variation === 'inverted'
      })}
    >
      {children.length > 0 ? (
        <span className="text-nowrap">{children}</span>
      ) : (
        <RichText
          tagName={'div'}
          className={``}
          placeholder={placeholder}
          value={link.title}
          allowedFormats={[]}
          onChange={(value) => {
            onChange({
              title: value
            });
          }}
          keepPlaceholderOnFocus
          disableLineBreaks={true}
        />
      )}
      {showLinkBox && (
        <LinkUI
          link={{
            title: link.title,
            url: link.url,
            opensInNewTab: link.opensInNewTab ?? false
          }}
          onClose={onClose}
          onChange={onChange}
          onRemove={onLinkRemove}
          anchor={anchor ?? buttonRef.current}
          focusOnMount={link.title === null || link.title.length === 0}
          extraOptions={() => {
            if (!onVariationSelect) {
              return null;
            }

            return (
              <ButtonVariationSelector
                variation={variation}
                onChange={onVariationSelect}
              />
            );
          }}
        />
      )}
    </div>
  );
}

const LinkUI = (props) => {
  const { saveEntityRecord } = useDispatch(store);

  async function handleCreate(pageTitle) {
    const page = await saveEntityRecord('postType', 'page', {
      title: pageTitle,
      status: 'draft'
    });

    return {
      id: page.id,
      type: 'page',
      title: decodeEntities(page.title.rendered),
      url: page.link,
      kind: 'post-type'
    };
  }

  return (
    <Popover
      offset={24}
      placement="bottom"
      onClose={props.onClose}
      anchor={props.anchor}
      shift
      focusOnMount={false}
    >
      <div role="dialog">
        <LinkControl
          hasTextControl
          value={props.link}
          showInitialSuggestions
          withCreateSuggestion={true}
          createSuggestion={handleCreate}
          suggestionsQuery={{
            type: 'post',
            subtype: 'page,post'
          }}
          onChange={(link) => {
            props.onChange({
              url: link.url,
              opensInNewTab: link.opensInNewTab ?? false,
              title: link.title
            });
          }}
          onRemove={props.onRemove}
          renderControlBottom={props.extraOptions}
        />
      </div>
    </Popover>
  );
};

const ButtonVariationSelector = (props) => {
  return (
    <VStack className="link-ui-tools">
      <ToggleGroupControl
        label="Button Variation"
        value={props.variation}
        isBlock
        __nextHasNoMarginBottom
        __next40pxDefaultSize
        onChange={props.onChange}
      >
        <ToggleGroupControlOption value="primary" label="Primary" />
        <ToggleGroupControlOption value="secondary" label="Secondary" />
        <ToggleGroupControlOption value="tertiary" label="Tertiary" />
        <ToggleGroupControlOption value="inverted" label="Inverted" />
      </ToggleGroupControl>
    </VStack>
  );
};

import { __ } from '@wordpress/i18n';

import {
  Inserter,
  useBlockProps,
  useInnerBlocksProps,
  store as blockEditorStore
} from '@wordpress/block-editor';
import { useSelect } from '@wordpress/data';

import { Button } from '../components';

export default function Edit({
  attributes,
  setAttributes,
  clientId,
  isSelected
}) {
  const showAppender = useSelect(
    (select) => {
      const buttons = select(blockEditorStore).getBlock(clientId)?.innerBlocks;

      return (
        buttons?.length < 1 ||
        select(blockEditorStore).hasSelectedInnerBlock(clientId) ||
        isSelected
      );
    },
    [isSelected]
  );

  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    useBlockProps({ className: 'not-prose' }),
    {
      allowedBlocks: ['takt/button']
    }
  );

  return (
    <div {...innerBlocksProps}>
      <div className="flex gap-4">
        {children}
        {showAppender && (
          <Inserter
            position="bottom center"
            __experimentalIsQuick
            className="w-full"
            rootClientId={clientId}
            renderToggle={({ onToggle, disabled }) => (
              <Button
                className="cursor-pointer opacity-60 hover:opacity-100"
                onClick={onToggle}
              >
                Add Button
              </Button>
            )}
            isAppender
          />
        )}
      </div>
    </div>
  );
}

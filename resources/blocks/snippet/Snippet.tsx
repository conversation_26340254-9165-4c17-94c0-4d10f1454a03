import { __ } from '@wordpress/i18n';

import { useBlockProps } from '@wordpress/block-editor';
import BlockHeader from '../components/BlockHeader';
import { TextareaControl, ToggleControl } from '@wordpress/components';
import { useState } from '@wordpress/element';

export default function Edit({ attributes, setAttributes }) {
  const { code } = attributes;

  const [preview, setPreview] = useState(false);

  return (
    <div className="container relative flex flex-col gap-y-16">
      <div
        {...useBlockProps({
          className: ''
        })}
      >
        <div className="flex flex-col gap-y-16">
          <BlockHeader
            setAttributes={setAttributes}
            header={attributes.header}
          />
          <div className="bg-gray p-12">
            <TextareaControl
              label="HTML"
              value={attributes.code}
              onChange={(value) => setAttributes({ code: value })}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

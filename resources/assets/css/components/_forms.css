@layer base {
  /* Global styles for radio buttons */
  input[type="radio"] {
    @apply appearance-none border border-black rounded-full w-[18px] h-[18px] flex justify-center items-center transition-all duration-300 cursor-pointer bg-white min-w-[18px];
  }

  input[type="radio"]:checked {
    @apply border-black bg-white;
  }

  input[type="radio"]:checked::before {
    @apply content-[''] block w-2 h-2 rounded-full bg-teal;
  }

  input[type="radio"]:focus {
    @apply outline-none;
  }

  input[type="radio"]:checked + label, input[type="radio"]:checked + span {
    @apply font-bold;
  }

  input[type="checkbox"] {
    @apply appearance-none bg-white border border-black rounded-sm !w-5 h-5 min-w-5 cursor-pointer flex justify-center items-center transition-all duration-300 shadow-none;
  }

  input[type="checkbox"]:checked {
      @apply bg-white border-black border;
  }

  input[type="checkbox"] + label {
    @apply max-w-[70vw];
  }

  input[type="checkbox"]:checked + label {
    @apply font-normal;
  }

  input[type="checkbox"]:checked::before {
      @apply content-['✓'] block w-3 h-3 bg-white text-teal leading-3;
  }

  input[type="checkbox"]:focus {
      @apply outline-none appearance-none;
  }
}

/*
  Gforms Specific:
*/
.gform-body {
  select {
    @apply !bg-white appearance-none bg-no-repeat pr-[2.5rem];
    background-image: url("data:image/svg+xml,%3Csvg width='14' height='10' viewBox='0 0 14 10' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 1l6 6 6-6' stroke='%23333' stroke-width='2' fill='none' fill-rule='evenodd'/%3E%3C/svg%3E");
    background-position: right 1rem center;
    background-size: 0.5em auto;
  }
}
fieldset.gfield--type-choice {

  .ginput_container_consent {
    @apply flex flex-row gap-x-2 items-center;
  }

  a {
    @apply text-navy hover:underline transition-all duration-300;
  }
}

import { __ } from '@wordpress/i18n';

import {
  useBlockProps,
  useInnerBlocksProps,
  store as blockEditorStore
} from '@wordpress/block-editor';
import { useSelect } from '@wordpress/data';
import { useState } from '@wordpress/element';

import { PlaceholderImage } from '../components';
import { Button } from '../components';
import { _class } from '../components/_utils';

export default function Header({ attributes, setAttributes, clientId }) {
  const { logo, button } = attributes;

  const blocks = useSelect(
    (select) => select(blockEditorStore).getBlock(clientId).innerBlocks,
    []
  );

  const { children, ...innerBlocksProps } = useInnerBlocksProps({
    allowedBlocks: ['core/navigation'],
    template: [['core/navigation', { orientation: 'horizontal', lock: false }]],
    renderAppender: false
  });

  const [isButtonLinkOpen, setIsButtonLinkOpen] = useState(false);

  return (
    <>
      <div
        {...useBlockProps({
          className: 'h-full w-full'
        })}
      >
        <div className="flex w-full items-center justify-between p-6">
          <div className="flex items-center justify-between gap-10">
            <PlaceholderImage
              image={logo}
              className=""
              placeholderClass="flex shrink-0 items-center"
              imageClass="flex shrink-0 items-center"
              onSelect={(media) => {
                setAttributes({
                  logo: {
                    id: media.id,
                    url: media.url
                  }
                });
              }}
              onDelete={() => {
                setAttributes({
                  logo: {
                    id: null,
                    url: null
                  }
                });
              }}
            />
          </div>
          <div {...innerBlocksProps}>{children}</div>

          {button && (
            <Button
              className="btn btn-primary"
              link={button}
              onChange={(value) => {
                setAttributes({
                  button: {
                    ...button,
                    ...value
                  }
                });

                setIsButtonLinkOpen(true);
              }}
              onClick={() => {
                setIsButtonLinkOpen(true);
              }}
              showLinkBox={isButtonLinkOpen}
              onClose={() => setIsButtonLinkOpen(false)}
              onLinkRemove={() => setIsButtonLinkOpen(false)}
            />
          )}
        </div>
      </div>
    </>
  );
}

import { __ } from '@wordpress/i18n';

import {
  RichText,
  useBlockProps,
  useInnerBlocksProps
} from '@wordpress/block-editor';

import { _class } from '../components/_utils';

import useEmbedUrl from '../components/_use-embed-url';

export default function SmallPageHeader({ attributes, setAttributes }) {
  const {
    title,
    image,
    hasBreadcrumbs,
    mediaType,
    video,
    videoSource,
    videoUrl
  } = attributes;

  const { embedUrl, videoId } = useEmbedUrl(videoUrl, videoSource);

  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    {
      className:
        'flex break-words w-full shrink-0 flex-col justify-between gap-y-8 rounded rounded-b-none bg-teal p-8 laptop:w-96'
    },
    {
      allowedBlocks: ['core/paragraph', 'takt/button', 'takt/button-group'],
      template: [['core/paragraph', { placeholder: 'Content...' }]]
    }
  );

  return (
    <div {...useBlockProps({ className: 'not-prose sm:max-w-none' })}>
      <div className="container container-wide">
        <div className="relative flex min-h-[529px] items-end justify-between gap-3 overflow-hidden rounded bg-gradient-navy-to-light-blue pl-4 pr-4 laptop:pl-16 laptop:pr-0">
          {mediaType === 'image' ? (
            <figure className="absolute left-0 h-full w-full">
              <img
                className="top-0 h-full w-full object-cover mix-blend-multiply"
                src={image.url}
                alt={image.alt}
              />
            </figure>
          ) : (
            <>
              {videoSource === 'local' ? (
                <>
                  <div className="absolute inset-0 h-full w-full overflow-hidden bg-gradient-navy-to-light-blue"></div>
                  <video
                    className="absolute inset-0 h-full w-full object-cover opacity-60"
                    autoPlay
                    loop
                    muted
                    key={video.id}
                  >
                    <source src={video.src} type={video.mime ?? ''} />
                  </video>
                </>
              ) : (
                embedUrl && (
                  <>
                    <div className="absolute inset-0 h-full w-full overflow-hidden bg-gradient-navy-to-light-blue"></div>
                    <iframe
                      src={`${embedUrl}?enablejsapi=1&controls=0&autoplay=1&loop=1&mute=1&playlist=${videoId}`}
                      className="pointer-events-none absolute left-1/2 top-1/2 h-[56.25vw] min-h-full w-[177.77777778vh] min-w-full -translate-x-1/2 -translate-y-1/2 opacity-60"
                      allow="autoplay; fullscreen"
                      allowFullScreen
                    ></iframe>
                  </>
                )
              )}
            </>
          )}

          <div className="z-10 flex min-h-80 w-full flex-col justify-between laptop:flex-row">
            {hasBreadcrumbs && title ? (
              <div className="flex flex-col gap-y-6 pb-16 pt-6 laptop:self-end laptop:pt-28">
                <div className="flex items-center gap-3">
                  <span className="h-5 w-5 rounded-full bg-teal"></span>

                  <p className="text-base font-bold uppercase tracking-ultra text-white">
                    {__('Breadcrumb', 'takt')}
                  </p>
                </div>
                <RichText
                  tagName="div"
                  className={`text-4xl text-white laptop:text-heading`}
                  placeholder={__('Page Title...', 'takt')}
                  value={title}
                  allowedFormats={[]}
                  onChange={(value) => {
                    setAttributes({ title: value });
                  }}
                  keepPlaceholderOnFocus
                  disableLineBreaks={true}
                />
              </div>
            ) : (
              <RichText
                tagName="div"
                className={`font-heading text-heading text-white`}
                placeholder={__('Page Title...', 'takt')}
                value={title}
                allowedFormats={[]}
                onChange={(value) => {
                  setAttributes({ title: value });
                }}
                keepPlaceholderOnFocus
                disableLineBreaks={true}
              />
            )}
            <div {...innerBlocksProps}>{children}</div>
          </div>
        </div>
      </div>
    </div>
  );
}

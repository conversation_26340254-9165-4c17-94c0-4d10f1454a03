import { __ } from '@wordpress/i18n';

import {
  RichText,
  useInnerBlocksProps,
  useBlockProps
} from '@wordpress/block-editor';
import { useEntityProp, store as coreStore } from '@wordpress/core-data';
import { useSelect } from '@wordpress/data';

import { _class } from '../../components/_utils';
import { PlaceholderImage } from '../../components';

export default function Edit(props) {
  const { setAttributes, context } = props;

  const [meta, updateMeta] = useEntityProp(
    'postType',
    'job-listing',
    'meta',
    context.postId
  );

  const [title, updateTitle] = useEntityProp(
    'postType',
    'job-listing',
    'title',
    context.postId
  );

  const [imageId, updateImageId] = useEntityProp(
    'postType',
    'job-listing',
    'featured_media',
    context.postId
  );
  const image = useSelect(
    (select) => select(coreStore).getMedia(imageId),
    [imageId]
  );

  const [excerpt, updateExcerpt] = useEntityProp(
    'postType',
    'job-listing',
    'excerpt',
    context.postId
  );

  const { job_id, company } = meta;

  if (props.attributes.description.length > 0 && excerpt.length === 0) {
    updateExcerpt(props.attributes.description);
  }

  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    {
      className: 'flex gap-x-4 mt-8'
    },
    {
      allowedBlocks: ['takt/button-group', 'takt/button'],
      template: [['takt/button-group']],
      templateLock: false
    }
  );

  return (
    <div {...useBlockProps({ className: 'not-prose sm:max-w-none' })}>
      <div className="container container-wide">
        <div className="not-prose relative gap-6 overflow-hidden rounded bg-gray sm:max-w-none">
          <div className="flex gap-6">
            <div className="flex-1 pb-16 pl-16 pt-28">
              <div className="mb-8 flex items-center gap-3">
                <span className="h-5 w-5 rounded-full bg-teal"></span>

                <span className="text-base font-bold uppercase tracking-ultra text-black">
                  {__('Breadcrumb', 'takt')}
                </span>
              </div>
              <RichText
                tagName="div"
                className={`mb-2 block text-2xl not-italic tablet:text-3xl`}
                placeholder={__('Company...', 'takt')}
                value={company}
                allowedFormats={[]}
                onChange={(value) => {
                  updateMeta({
                    company: value
                  });
                }}
                keepPlaceholderOnFocus
                disableLineBreaks={true}
              />
              <RichText
                tagName="div"
                className={`text-3xl text-navy tablet:text-4xl`}
                placeholder={__('Job Title...', 'takt')}
                value={title}
                allowedFormats={[]}
                onChange={(value) => {
                  updateTitle(value);
                }}
                keepPlaceholderOnFocus
                disableLineBreaks={true}
              />

              <RichText
                tagName="div"
                className={`mt-2 text-base`}
                placeholder={__('Short description...', 'takt')}
                value={excerpt}
                allowedFormats={[]}
                onChange={(value) => {
                  updateExcerpt(value);
                }}
                keepPlaceholderOnFocus
                disableLineBreaks={true}
              />
              <PlaceholderImage
                image={{
                  id: image?.id,
                  url: image?.source_url
                }}
                className="mt-4 w-fit"
                imageClass="w-full max-h-[75px]"
                placeholderClass="w-24 h-auto aspect-video"
                onSelect={(image) => {
                  updateImageId(image.id);
                }}
                onDelete={() => {
                  updateImageId(null);
                }}
              />
              {/* <div className="my-8 flex gap-x-4 border-b pb-6">
                <div className="inline-flex bg-teal p-2 text-black">
                  <span>ID#</span>
                  <RichText
                    tagName="div"
                    className={`ml-[1ch]`}
                    placeholder={__('00-0000', 'takt')}
                    value={job_id}
                    allowedFormats={[]}
                    onChange={(value) => {
                      updateMeta({
                        job_id: value
                      });
                    }}
                    keepPlaceholderOnFocus
                    disableLineBreaks={true}
                  />
                </div>
              </div> */}
              <div {...innerBlocksProps}>{children}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

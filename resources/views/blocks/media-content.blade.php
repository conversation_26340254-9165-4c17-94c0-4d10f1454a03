<x-block :block="$block" @class([
    'container relative',
])>
    @unless ($hasBgGraphic === false)
        <div
            role="presentation"
            class="pointer-events-none absolute left-3/4 top-1/2 -z-10 aspect-square max-h-full w-full -translate-y-1/2 scale-125 overflow-hidden bg-radial bg-contain bg-no-repeat"
        ></div>
    @endunless

    <div class="relative z-20 space-y-12 lg:space-y-16">
        @unless (empty($attributes['eyebrow']))
            <div
                class="flex w-full flex-col border-t border-current pt-3 laptop:flex-row"
            >
                <div class="flex flex-col gap-y-8">
                    <div class="flex items-start gap-3">
                        <span
                            class="size-5 flex-none rounded-full bg-teal"
                        ></span>
                        <p class="text-base font-bold uppercase tracking-ultra">
                            {!! $attributes['eyebrow'] !!}
                        </p>
                    </div>
                </div>
            </div>
        @endunless

        <div
            @class([
                'relative z-20 flex flex-col gap-x-8 gap-y-12 overflow-hidden tablet:grid tablet:min-h-[32rem] tablet:justify-between',
                'grid-cols-2' => $layout === 'fifty-fifty',
                'grid-flow-row-dense grid-cols-10' => $layout !== 'fifty-fifty',
            ])
        >
            @unless (empty($title))
                <div
                    @class([
                        'flex flex-col',
                        'col-span-7' => $layout !== 'fifty-fifty',
                    ])
                >
                    <h3 class="text-3xl text-navy tablet:text-4xl">
                        {!! $title !!}
                    </h3>

                    <div class="prose mt-4 flex-1">
                        {!! $children !!}
                    </div>
                </div>
            @endunless

            @if ($mediaType === 'image')
                @if (! empty($image['url']))
                    <div
                        @class([
                            'min-h-full',
                            'col-span-3' => $layout !== 'fifty-fifty',
                            'tablet:order-first' => $mediaPosition === 'left',
                        ])
                    >
                        <figure
                            class="relative h-full"
                            style="
                                --position: {{ $image['imagePosition'] ?? 'center center' }};
                            "
                        >
                            <x-image
                                :image="$image"
                                class="has-circle top-0 max-h-[80vh] min-h-full w-full rounded object-cover object-[--position]"
                            />
                        </figure>
                    </div>
                @endif
            @else
                @if ($videoSource === 'local' && ! empty($video['src']))
                    <div
                        @class([
                            'has-circle min-h-full overflow-hidden',
                            'col-span-3' => $layout !== 'fifty-fifty',
                            'tablet:order-first' => $mediaPosition === 'left',
                        ])
                    >
                        <video
                            class="has-circle min-h-full w-full rounded object-cover"
                            autoplay
                            loop
                            muted
                        >
                            <source
                                src="{{ $video['src'] }}"
                                type="{{ $video['mime'] }}"
                            />
                        </video>
                    </div>
                @elseif (! empty($attributes['videoUrl']))
                    @php
                        $embedUrl = preg_replace(
                            '/^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/(?:watch\?v=)?([^&\s]+)/',
                            'https://www.youtube.com/embed/$4',
                            $attributes['videoUrl'],
                        );
                    @endphp

                    <div
                        @class([
                            'has-circle min-h-full overflow-hidden',
                            'col-span-3' => $layout !== 'fifty-fifty',
                            'tablet:order-first' => $mediaPosition === 'left',
                        ])
                    >
                        <iframe
                            src="{!! $embedUrl !!}"
                            class="min-h-full min-w-full rounded object-cover"
                            allow="autoplay; fullscreen"
                            allowfullscreen
                        ></iframe>
                    </div>
                @endif
            @endif
        </div>
    </div>
</x-block>

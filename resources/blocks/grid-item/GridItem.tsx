import { __ } from '@wordpress/i18n';

import {
  RichText,
  useBlockProps,
  useInnerBlocksProps,
  InspectorControls
} from '@wordpress/block-editor';

import BlockHeader from '../components/BlockHeader';
import { _class } from '../components/_utils';

import { isDark } from '../components/_utils';

import {
  PanelBody,
  __experimentalVStack as VStack,
  __experimentalToggleGroupControl as ToggleGroupControl,
  __experimentalToggleGroupControlOption as ToggleGroupControlOption
} from '@wordpress/components';

export default function Edit({ attributes, setAttributes }) {
  const {
    backgroundType,
    image,
    headerType,
    columns,
    eyebrow,
    title,
    description
  } = attributes;
  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    {
      className: _class({
        default: 'z-10 grid gap-4',
        'grid-cols-[repeat(3,_minmax(0,_1fr))]': columns == 3,
        'grid-cols-[repeat(4,_minmax(0,_1fr))]': columns == 4,
        'px-16': headerType === 'green'
      })
    },
    {
      allowedBlocks: [
        ['takt/stats'],
        ['takt/contact-card'],
        ['takt/media-card']
      ]
    }
  );

  return (
    <>
      <InspectorControls>
        <PanelBody title="Grid Settings">
          <VStack spacing={1}>
            <ToggleGroupControl
              label="Background Type"
              value={backgroundType}
              isBlock
              __nextHasNoMarginBottom
              __next40pxDefaultSize
              onChange={(value) => {
                setAttributes({ backgroundType: value });
              }}
            >
              <ToggleGroupControlOption value="image" label="Image" />
              <ToggleGroupControlOption value="gradient" label="Gradient" />
            </ToggleGroupControl>
            <ToggleGroupControl
              label="Header Type"
              value={headerType}
              isBlock
              __nextHasNoMarginBottom
              __next40pxDefaultSize
              onChange={(value) => {
                setAttributes({ headerType: value });
              }}
            >
              <ToggleGroupControlOption value="default" label="Default" />
              <ToggleGroupControlOption value="green" label="Green Box" />
            </ToggleGroupControl>
            <ToggleGroupControl
              label="Columns"
              value={columns}
              isBlock
              __nextHasNoMarginBottom
              __next40pxDefaultSize
              onChange={(value) => {
                setAttributes({ columns: value });
              }}
            >
              <ToggleGroupControlOption value="3" label="3 Columns" />
              <ToggleGroupControlOption value="4" label="4 Columns" />
            </ToggleGroupControl>
          </VStack>
        </PanelBody>
      </InspectorControls>
      <div
        {...useBlockProps({
          className: _class({
            default:
              'container relative flex flex-col gap-y-16 overflow-hidden',
            'px-16 py-16': headerType === 'default',
            '': headerType === 'green',
            'bg-navy text-white': backgroundType === 'image',
            'bg-gradient-navy-to-light-blue text-white':
              backgroundType === 'gradient'
          })
        })}
      >
        {backgroundType === 'image' && image.url && (
          <>
            <div className="absolute inset-0 z-[1] h-full w-full overflow-hidden bg-navy opacity-80"></div>
            <figure className="absolute inset-0 h-full w-full object-cover opacity-60">
              <img
                className="left-0 top-0 h-full w-full object-cover"
                src={image.url}
                alt={image.alt}
              />
            </figure>
          </>
        )}

        <div className="z-10 flex flex-col gap-y-16">
          {headerType === 'default' ? (
            <BlockHeader
              setAttributes={setAttributes}
              header={attributes.header}
              background="bg-navy"
            />
          ) : (
            <div className="left-0 top-0 flex max-w-3xl flex-col gap-y-16 bg-teal p-16 text-black">
              <div className="flex w-full flex-col laptop:flex-row">
                <div className="flex flex-col gap-y-8">
                  <div className="flex items-center gap-3">
                    <RichText
                      tagName="p"
                      className="text-base font-bold uppercase tracking-ultra"
                      placeholder={__('Eyebrow text...', 'takt')}
                      value={eyebrow}
                      allowedFormats={[]}
                      onChange={(value) => setAttributes({ eyebrow: value })}
                      keepPlaceholderOnFocus
                      disableLineBreaks={true}
                    />
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-y-6">
                <div className="flex shrink-0 gap-x-4">
                  <RichText
                    tagName="h3"
                    className={`text-3xl`}
                    placeholder={__('Title...', 'takt')}
                    value={title}
                    allowedFormats={[]}
                    onChange={(value) => setAttributes({ title: value })}
                    keepPlaceholderOnFocus
                    disableLineBreaks={true}
                  />
                </div>

                <div className="flex min-w-[300px] flex-col gap-y-6">
                  <RichText
                    tagName="div"
                    className={`text-base`}
                    placeholder={__('Text comes here...', 'takt')}
                    value={description}
                    allowedFormats={['core/bold', 'core/italic', 'core/link']}
                    onChange={(value) => setAttributes({ description: value })}
                    keepPlaceholderOnFocus
                    disableLineBreaks={true}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
        <div {...innerBlocksProps}>{children}</div>
      </div>
    </>
  );
}

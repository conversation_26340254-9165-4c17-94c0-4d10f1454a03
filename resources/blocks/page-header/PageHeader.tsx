import { __ } from '@wordpress/i18n';

import {
  RichText,
  useBlockProps,
  MediaUpload,
  useInnerBlocksProps,
  InspectorControls
} from '@wordpress/block-editor';

import {
  Button,
  PanelBody,
  ToggleControl,
  __experimentalHeading as Heading,
  __experimentalVStack as VStack,
  __experimentalToggleGroupControl as ToggleGroupControl,
  __experimentalToggleGroupControlOption as ToggleGroupControlOption,
  TextControl
} from '@wordpress/components';

import { _class } from '../components/_utils';

import LargePageHeader from './_large-page-header';
import SmallPageHeader from './_small-page-header';

export default function Edit(props) {
  const { cardSize, hasBreadcrumbs, mediaType, videoSource, video, videoUrl } =
    props.attributes;

  const { setAttributes } = props;

  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    {
      className: _class({
        default: 'container-wide container'
      })
    },
    {
      allowedBlocks: ['core/paragraph', 'takt/button', 'takt/button-group'],
      template: [['core/paragraph', { placeholder: 'Content...' }]]
    }
  );

  return (
    <>
      <InspectorControls>
        <PanelBody title="Block Settings">
          <VStack spacing={1}>
            <ToggleControl
              label="Has Breadcrumbs"
              checked={hasBreadcrumbs}
              onChange={(value) => setAttributes({ hasBreadcrumbs: value })}
              __nextHasNoMarginBottom
            />
            <ToggleGroupControl
              label="Media Type"
              value={mediaType}
              isBlock
              __nextHasNoMarginBottom
              __next40pxDefaultSize
              onChange={(value) => {
                setAttributes({ mediaType: value });
              }}
            >
              <ToggleGroupControlOption value="image" label="Image" />
              <ToggleGroupControlOption value="video" label="Video" />
            </ToggleGroupControl>
            {mediaType === 'video' && (
              <>
                <ToggleGroupControl
                  label="Video Source"
                  value={videoSource}
                  isBlock
                  __nextHasNoMarginBottom
                  __next40pxDefaultSize
                  onChange={(value) => {
                    setAttributes({ videoSource: value });
                  }}
                >
                  <ToggleGroupControlOption value="local" label="Upload" />
                  <ToggleGroupControlOption value="youtube" label="YouTube" />
                  <ToggleGroupControlOption value="vimeo" label="Vimeo" />
                </ToggleGroupControl>
                {videoSource !== 'local' ? (
                  <TextControl
                    label="Video URL"
                    value={videoUrl}
                    onChange={(value) => {
                      setAttributes({ videoUrl: value });
                    }}
                  />
                ) : (
                  <MediaUpload
                    onSelect={(media) => {
                      setAttributes({
                        video: {
                          id: media.id,
                          src: media.url,
                          mime: media.mime
                        }
                      });
                    }}
                    allowedTypes={['video']}
                    value={video.id}
                    render={({ open }) => (
                      <>
                        <Heading className="mb-0">Video</Heading>
                        <div className="mb-4 flex gap-2">
                          <Button variant="primary" onClick={open}>
                            {!!video.src ? 'Replace Video' : 'Set Video'}
                          </Button>
                          {!!video.src && (
                            <Button
                              variant="secondary"
                              onClick={() => {
                                setAttributes({
                                  video: {
                                    id: null,
                                    src: null
                                  }
                                });
                              }}
                            >
                              Reset Video
                            </Button>
                          )}
                        </div>
                      </>
                    )}
                  />
                )}
              </>
            )}
            <ToggleGroupControl
              label="Card Size"
              value={cardSize}
              isBlock
              __nextHasNoMarginBottom
              __next40pxDefaultSize
              onChange={(value) => {
                setAttributes({ cardSize: value });
              }}
            >
              <ToggleGroupControlOption value="small" label="Small" />
              <ToggleGroupControlOption value="large" label="Large" />
            </ToggleGroupControl>
          </VStack>
        </PanelBody>
      </InspectorControls>
      <div
        {...useBlockProps()}
      >
        {cardSize === 'large' ? (
          <LargePageHeader {...props} />
        ) : (
          <SmallPageHeader {...props} />
        )}
      </div>
    </>
  );
}

<x-block :block="$block" @class([
    'container-wide container',
])>
    <div
        class="relative flex h-full justify-between gap-3 overflow-hidden rounded bg-gradient-navy-to-light-blue px-0 md:pl-4 md:pr-0 lg:pl-16 laptop:items-end landscape:min-h-[529px]"
    >
        @if ($mediaType === 'image')
            @if (! empty($image['url']))
                <figure class="absolute left-0 h-full w-full">
                    <x-image
                        :image="$image"
                        class="top-0 h-full w-full object-cover opacity-40 mix-blend-multiply"
                    />
                </figure>
            @endif
        @elseif ($mediaType === 'video')
            @if ($videoSource === 'local' && ! empty($video['src']))
                <div
                    class="absolute inset-0 h-full w-full overflow-hidden bg-gradient-navy-to-light-blue"
                ></div>
                <video
                    class="absolute inset-0 h-full w-full object-cover opacity-60"
                    autoplay
                    loop
                    muted
                    playsinline
                >
                    <source
                        src="{{ $video['src'] }}"
                        type="{{ $video['mime'] }}"
                    />
                </video>
            @elseif (! empty($videoUrl))
                <div
                    class="absolute inset-0 h-full w-full overflow-hidden bg-gradient-navy-to-light-blue"
                ></div>
                <iframe
                    src="{!! $videoUrl !!}?controls=0&autoplay=1&loop=1&mute=1"
                    class="pointer-events-none absolute left-1/2 top-1/2 h-[56.25vw] min-h-full w-[177.77777778vh] min-w-full -translate-x-1/2 -translate-y-1/2 opacity-60"
                    allow="autoplay; fullscreen"
                    allowfullscreen
                ></iframe>
            @endif
        @endif

        <div
            class="z-10 flex h-full min-h-80 w-full flex-col justify-between gap-x-6 laptop:flex-row"
        >
            @if ($hasBreadcrumbs || $title)
                <div
                    class="flex w-full flex-col gap-y-6 px-6 pb-16 pt-6 laptop:min-h-[0px] laptop:self-end laptop:pl-0 laptop:pr-6 laptop:pt-28"
                >
                    @if ($hasBreadcrumbs)
                        <div class="flex items-start gap-3">
                            <span
                                class="h-5 w-5 flex-none rounded-full bg-teal"
                            ></span>
                            @php(yoast_breadcrumb('<p class="text-white text-sm font-bold uppercase tracking-ultra" id="breadcrumbs">', '</p>'))
                        </div>
                    @endif

                    @unless (empty($title))
                        <h1
                            class="text-4xl/none text-white laptop:text-heading"
                        >
                            {!! $title !!}
                        </h1>
                    @endunless
                </div>
            @endif

            <div
                class="flex w-full shrink-0 flex-col justify-between gap-y-8 break-words rounded rounded-b-none rounded-t-none bg-teal px-6 py-8 laptop:w-96 laptop:py-16"
            >
                {!! $children !!}
            </div>
        </div>
    </div>
</x-block>

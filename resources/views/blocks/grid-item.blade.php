@php
    $eyebrow = $block->attributes['eyebrow'] ?? '';
    $title = $block->attributes['title'] ?? '';
    $description = $block->attributes['description'] ?? '';
    $header = $block->attributes['header'] ?? [];
    $columns = $block->attributes['columns'] ?? 3;
    $backgroundType = $block->attributes['backgroundType'] ?? 'image';
    $headerType = $block->attributes['headerType'] ?? 'default';
    $image = $block->attributes['image'] ?? [];
@endphp

<div class="container container-wide">
    <x-block
        :block="$block"
        @class([
            'relative overflow-hidden rounded',
            'bg-navy text-white' => $backgroundType === 'image',
            'bg-gradient-navy-to-light-blue text-white' => $backgroundType === 'gradient',
        ])
    >
        <div
            @class([
                'grid' => $headerType === 'default',
                'grid' => $headerType === 'green',
            ])
        >
            @if ($backgroundType === 'image' && ! empty($image['url']))
                <div
                    class="pointer-events-none absolute inset-0 z-10 h-full w-full overflow-hidden bg-navy opacity-80"
                ></div>
                <figure
                    role="presentation"
                    class="pointer-events-none absolute inset-0 h-full w-full object-cover opacity-60"
                >
                    <x-image
                        :image="$image"
                        class="left-0 top-0 h-full w-full object-cover"
                    />
                </figure>
            @endif

            <div
                @class([
                    'relative z-20',
                    'px-6 tablet:px-10 laptop:px-16' => $headerType === 'default',
                ])
            >
                @if ($headerType === 'default')
                    <x-block-header
                        :$header
                        class="mb-14 pb-0 pt-0 lg:mb-16"
                        background="bg-navy"
                    />
                @else
                    <div
                        @class([
                            'z-20 mb-10 space-y-6 rounded-b bg-teal px-6 py-9 text-black md:mb-16 md:space-y-10 tablet:px-16 tablet:py-16 lg:mb-28 lg:space-y-16',
                            'tablet:w-[calc(75%-0.5rem)]' => $columns === '4',
                            'tablet:w-[calc(100%/3*2-1.75rem)]' => $columns === '3',
                        ])
                    >
                        @unless (empty($eyebrow))
                            <p
                                class="text-sm font-bold uppercase tracking-wide"
                            >
                                {{ $eyebrow }}
                            </p>
                        @endunless

                        @unless (empty($title) || empty($description))
                            <div class="flex-1 shrink-0">
                                <h3 class="text-3xl md:text-4xl">
                                    {!! $title !!}
                                </h3>
                                <p class="mt-6">
                                    {!! $description !!}
                                </p>
                            </div>
                        @endunless
                    </div>
                @endif

                <div
                    @class([
                        'z-10 flex flex-col gap-x-4 gap-y-6 sm:grid sm:grid-cols-2 md:gap-y-12',
                        'px-6 md:px-10 lg:px-16' => $headerType !== 'default',
                        'lg:grid-cols-3' => $columns == '3',
                        'lg:grid-cols-4' => $columns == '4',
                    ])
                >
                    {!! $children !!}
                </div>
            </div>
        </div>
    </x-block>
</div>

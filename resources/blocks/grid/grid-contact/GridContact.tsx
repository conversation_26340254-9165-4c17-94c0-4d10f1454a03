import { __ } from '@wordpress/i18n';

import { RichText, useBlockProps } from '@wordpress/block-editor';

import { _class } from '../../components/_utils';

export default function Edit({ attributes, setAttributes }) {
  const { eyebrow, location, address, phone1, phone2, email } = attributes;

  return (
    <>
      <div className="flex flex-col gap-y-4 bg-white p-8 bite bite-x-1.5 bite-y-1.5 bite-6">
        <div className="flex flex-col gap-2">
          <RichText
            tagName="p"
            className="text-base font-bold uppercase tracking-ultra text-black"
            placeholder={__('Eyebrow text...', 'takt')}
            value={eyebrow}
            allowedFormats={[]}
            onChange={(value) => setAttributes({ eyebrow: value })}
            keepPlaceholderOnFocus
            disableLineBreaks={true}
          />
          <RichText
            tagName="p"
            className="text-xl font-bold leading-9 text-navy"
            placeholder={__('Location', 'takt')}
            value={location}
            allowedFormats={[]}
            onChange={(value) => setAttributes({ location: value })}
            keepPlaceholderOnFocus
          />
        </div>
        <div className="flex flex-col gap-y-4">
          <RichText
            tagName="p"
            placeholder={__('Address', 'takt')}
            value={address}
            allowedFormats={[]}
            onChange={(value) => setAttributes({ address: value })}
            keepPlaceholderOnFocus
          />
        </div>

        <div className="flex flex-col gap-y-2">
          <RichText
            tagName="p"
            placeholder={__('Phone 1', 'takt')}
            value={phone1}
            allowedFormats={[]}
            onChange={(value) => setAttributes({ phone1: value })}
            keepPlaceholderOnFocus
            disableLineBreaks={true}
          />
          <RichText
            tagName="p"
            placeholder={__('Phone 2', 'takt')}
            value={phone2}
            allowedFormats={[]}
            onChange={(value) => setAttributes({ phone2: value })}
            keepPlaceholderOnFocus
            disableLineBreaks={true}
          />
        </div>
        <div className="flex flex-col gap-y-2">
          <RichText
            tagName="p"
            placeholder={__('Email', 'takt')}
            value={email}
            allowedFormats={[]}
            onChange={(value) => setAttributes({ email: value })}
            keepPlaceholderOnFocus
            disableLineBreaks={true}
          />
        </div>
      </div>
    </>
  );
}

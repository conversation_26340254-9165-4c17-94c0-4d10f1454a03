export default function news(perPageOptions: number[]) {
  return {
    selectedFilters: {
      category: '',
      sort: 'date'
    },
    perPage: perPageOptions[1],
    searchQuery: '',
    categories: [],
    page: 1,
    total_count: 0,
    posts: [],
    filteredPosts: [],

    get totalPages() {
      return Math.ceil(this.total_count / this.perPage);
    },

    scrollToTop() {
      const element = document.querySelector('.news-listings');
      if (element) {
        window.scrollTo({
          top: element.offsetTop,
          behavior: 'smooth'
        });
      }
    },

    nextPage() {
      if (this.page < this.totalPages) {
        this.page++;
        this.applyFilters(false);
        this.scrollToTop();
      }
    },

    prevPage() {
      if (this.page > 1) {
        this.page--;
        this.applyFilters(false);
        this.scrollToTop();
      }
    },

    clearFilters() {
      this.selectedFilters = {
        category: '',
        sort: 'date'
      };
      this.searchQuery = '';
      this.page = 1;
      this.applyFilters();
    },

    updateUrl() {
      const params = new URLSearchParams();
      
      if (this.searchQuery) {
        params.set('search', this.searchQuery);
      }
      
      Object.keys(this.selectedFilters).forEach((key) => {
        if (this.selectedFilters[key]) {
          params.set(key, this.selectedFilters[key]);
        }
      });
      
      if (this.page > 1) {
        params.set('page', this.page.toString());
      }
      
      const newUrl = params.toString() 
        ? `${window.location.pathname}?${params.toString()}`
        : window.location.pathname;
      
      window.history.replaceState({}, '', newUrl);
    },

    async fetchAllResources(
      searchQuery = '',
      filters = {},
      page = 1,
      perPage = 12
    ) {
      const params = new URLSearchParams();

      if (searchQuery) {
        params.set('search', searchQuery);
      }

      Object.keys(filters).forEach((key) => {
        if (filters[key]) {
          params.set(key, filters[key]);
        }
      });

      params.set('page', page.toString());
      params.set('perPage', perPage.toString());

      const response = await fetch(
        `/api/takt/v1/posts?${params.toString()}`
      );
      const data = await response.json();
      return data;
    },

    applyFilters(resetPage = true) {
      if (!Array.isArray(this.posts)) return;

      let filters = this.selectedFilters;
      let searchQuery = this.searchQuery ? this.searchQuery.toLowerCase() : '';

      this.fetchAllResources(
        searchQuery,
        filters,
        this.page,
        this.perPage
      ).then((data) => {
        this.filteredPosts = data.posts;
        this.total_count = data.total_count;

        if (resetPage) {
          this.page = 1;
        }
        this.updateUrl();
      });
    },

    async init() {
      try {
        let urlParams = new URLSearchParams(window.location.search);
        urlParams.forEach((value, key) => {
          if (key === 'search') {
            this.searchQuery = value;
          } else if (key === 'page') {
            this.page = parseInt(value, 10);
          } else {
            this.selectedFilters[key] = value;
          }
        });

        const data = await this.fetchAllResources();
        this.posts = data.posts;

        this.filteredPosts = [...this.posts];
        this.total_count = data.total_count;

        // Fetch categories
        const categoryResponse = await fetch('/wp-json/wp/v2/categories?per_page=100');
        const categories = await categoryResponse.json();

        this.categories = categories.map((category) => ({
          id: category.id,
          name: category.name,
          slug: category.slug,
          count: category.count || 0
        }));

        this.applyFilters();
      } catch (error) {
        console.error('Error fetching news data:', error);
      }
    }
  };
}

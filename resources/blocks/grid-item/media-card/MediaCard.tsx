import { __ } from '@wordpress/i18n';

import { RichText, useBlockProps } from '@wordpress/block-editor';

export default function Edit({ attributes, setAttributes }) {
  const { image, eyebrow, title, description } = attributes;

  return (
    <div {...useBlockProps()}>
      <div className="has-circle flex flex-col gap-y-16 bg-white p-8">
        {image && image.url && (
          <figure className="relative max-h-28">
            <img
              className="aspect-square justify-self-end object-cover"
              src={image.url}
              alt={image.alt}
            />
          </figure>
        )}
        <div className="flex flex-col gap-2">
          <RichText
            tagName="p"
            className="text-base font-bold uppercase tracking-ultra text-black"
            placeholder={__('Eyebrow text...', 'takt')}
            value={eyebrow}
            allowedFormats={[]}
            onChange={(value) => setAttributes({ eyebrow: value })}
            keepPlaceholderOnFocus
            disableLineBreaks={true}
          />
          <RichText
            tagName="h3"
            className="text-xl font-bold leading-9 text-navy"
            placeholder={__('Title...', 'takt')}
            value={title}
            allowedFormats={[]}
            onChange={(value) => setAttributes({ title: value })}
            keepPlaceholderOnFocus
            disableLineBreaks={true}
          />
          <RichText
            tagName="p"
            className="text-black"
            placeholder={__('Description...', 'takt')}
            value={description}
            allowedFormats={['core/bold', 'core/italic', 'core/link']}
            onChange={(value) => setAttributes({ description: value })}
            keepPlaceholderOnFocus
          />
        </div>
      </div>
    </div>
  );
}

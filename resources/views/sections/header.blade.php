<header id="site-header" x-ref="header" class="banner">
    <div
        x-data="{
            open: false,
            toggle() {
                this.open = ! this.open
            },
        }"
        class="container container-wide max-lg:px-4"
    >
        <div class="flex w-full flex-col py-6">
            <div class="flex items-center justify-end gap-10">
                @if ($logo)
                    <a
                        href="{{ home_url('/') }}"
                        class="relative z-40 mr-auto flex shrink-0 items-center"
                    >
                        <img src="{{ $logo['url'] }}" class="block" />
                    </a>
                @endif

                <div class="relative z-40 lg:hidden">
                    <button
                        class="stack items-center justify-items-center p-2"
                        @click="toggle"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            class="size-6 transition-opacity"
                            :class="{
                                'opacity-0': open,
                            }"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"
                            />
                        </svg>
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="17"
                            class="size-4"
                            viewBox="0 0 17 17"
                            fill="none"
                            class="opacity-0 transition-opacity"
                            :class="{
                                'opacity-0': ! open,
                            }"
                        >
                            <path
                                d="M8.5 9.3685L2.06477 15.8037C1.95028 15.9182 1.80962 15.9796 1.64279 15.9877C1.47596 15.9959 1.32712 15.9346 1.19627 15.8037C1.06542 15.6729 1 15.5281 1 15.3695C1 15.2108 1.06542 15.0661 1.19627 14.9352L7.6315 8.5L1.19627 2.06477C1.08178 1.95028 1.02044 1.80962 1.01227 1.64279C1.00409 1.47596 1.06542 1.32712 1.19627 1.19627C1.32712 1.06542 1.47187 1 1.63052 1C1.78917 1 1.93392 1.06542 2.06477 1.19627L8.5 7.6315L14.9352 1.19627C15.0497 1.08178 15.1908 1.02044 15.3584 1.01227C15.5245 1.00409 15.6729 1.06542 15.8037 1.19627C15.9346 1.32712 16 1.47187 16 1.63052C16 1.78917 15.9346 1.93392 15.8037 2.06477L9.3685 8.5L15.8037 14.9352C15.9182 15.0497 15.9796 15.1908 15.9877 15.3584C15.9959 15.5245 15.9346 15.6729 15.8037 15.8037C15.6729 15.9346 15.5281 16 15.3695 16C15.2108 16 15.0661 15.9346 14.9352 15.8037L8.5 9.3685Z"
                                fill="black"
                                stroke="black"
                                stroke-width="0.25"
                            />
                        </svg>
                    </button>
                </div>

                <div
                    class="z-30 flex-1 items-center justify-end gap-4 transition-transform max-lg:fixed max-lg:inset-0 max-lg:h-screen max-lg:w-screen max-lg:translate-x-full max-lg:bg-gray max-lg:py-40 lg:flex xl:gap-10"
                    :class="{
                        'max-lg:translate-x-full': ! open
                    }"
                    x-trap.inert.noscroll="open"
                >
                    <div class="lg:flex">
                        {!! $children !!}
                    </div>

                    @if ($button)
                        <a
                            href="{{ $button['url'] }}"
                            class="btn btn-primary max-2xl:text-xs max-lg:ml-6 lg:max-xl:px-2 lg:max-xl:py-3"
                        >
                            <span>{{ $button['title'] }}</span>
                            <span>
                                <x-icons.squareArrow class="h-4 w-4" />
                            </span>
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</header>

import { __ } from '@wordpress/i18n';
import {
  useBlockProps,
  useInnerBlocksProps,
  InnerBlocks,
  store as blockEditorStore
} from '@wordpress/block-editor';
import { useEffect, useRef } from '@wordpress/element';
import BlockHeader from '../components/BlockHeader';
import { useSelect } from '@wordpress/data';

export default function Edit({ attributes, setAttributes, clientId }) {
  const blocks = useSelect(
    (select) => select(blockEditorStore).getBlock(clientId)?.innerBlocks,
    [clientId]
  );

  const panelsRef = useRef(null);

  const innerBlocksProps = useInnerBlocksProps(
    {
      className: `flex gap-x-4 justify-end items-stretch [counter-reset:item]`,
      ref: panelsRef
    },
    {
      allowedBlocks: ['takt/flyout-panel-item'],
      template: [
        ['takt/flyout-panel-item'],
        ['takt/flyout-panel-item'],
        ['takt/flyout-panel-item']
      ],
      renderAppender: false
    }
  );

  useEffect(() => {
    if (panelsRef.current) {
      setTimeout(() => {
        const panelsWidth =
          panelsRef.current.getBoundingClientRect().right -
          panelsRef.current.children[0].getBoundingClientRect().left;
        const remainingWidth = panelsRef.current.clientWidth - panelsWidth;

        panelsRef.current.style.setProperty(
          '--panels-width',
          `${remainingWidth}px`
        );
      }, 500);
    }
  }, [panelsRef]);

  return (
    <div
      {...useBlockProps({
        className: 'container relative flex flex-col gap-y-16 px-16 py-16'
      })}
    >
      <BlockHeader setAttributes={setAttributes} header={attributes.header} />
      <div {...innerBlocksProps}>{innerBlocksProps.children}</div>
    </div>
  );
}

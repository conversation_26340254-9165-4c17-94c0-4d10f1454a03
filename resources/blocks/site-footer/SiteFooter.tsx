import { __ } from '@wordpress/i18n';

import {
  MediaUpload,
  RichText,
  store as coreBlockEditorStore,
  useBlockProps,
  useInnerBlocksProps,
  InspectorControls
} from '@wordpress/block-editor';

import {
  Icon,
  PanelBody,
  SelectControl,
  TextControl,
  __experimentalDivider as Divider,
  Spinner,
  __experimentalHeading as Heading,
  __experimentalVStack as VStack,
  But<PERSON> as WPButton
} from '@wordpress/components';
import { store as coreStore, useEntityRecords } from '@wordpress/core-data';
import { useState } from '@wordpress/element';
import { useDispatch, useSelect } from '@wordpress/data';

import SocialLinks from './_socials';

import { PlaceholderImage, Button } from '../components';

export default function Footer({ attributes, setAttributes }) {
  const { logo, columnOne, columnTwo, footerText, legalNavigation, copyright } =
    attributes;

  const {
    hasResolved: navigationListResolved,
    records: navigationList
  }: { hasResolved: boolean; records: any[] | null } = useEntityRecords(
    'postType',
    'wp_navigation',
    {
      per_page: -1
    }
  );

  let legalNavObject = null;

  if (legalNavigation && navigationListResolved) {
    legalNavObject = navigationList.find((nav) => nav.id == legalNavigation);
  }

  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    {
      className: `footer-navigation my-10 border-y py-10`
    },
    {
      allowedBlocks: ['core/navigation'],
      template: [
        ['core/navigation', { orientation: 'horizontal', lock: false }]
      ],
      renderAppender: false
    }
  );

  return (
    <>
      <InspectorControls>
        <PanelBody title="Footer Settings">
          <VStack spacing={3}>
            <VStack spacing={1}>
              <Heading>Legal Navigation</Heading>
              {navigationListResolved ? (
                <SelectControl
                  label="Select Menu"
                  value={legalNavigation}
                  options={[
                    { label: 'Menus', value: 0 },
                    ...navigationList.map((nav) => ({
                      label: nav.title.rendered,
                      value: nav.id
                    }))
                  ]}
                  onChange={(value: string) =>
                    setAttributes({ legalNavigation: value })
                  }
                />
              ) : (
                <Spinner />
              )}
            </VStack>
            <Divider />
            <VStack spacing={1}>
              <Heading>Content</Heading>
              <TextControl
                label="Copyright"
                help="Use [year] to display the current year."
                value={copyright}
                onChange={(value) => setAttributes({ copyright: value })}
              />
            </VStack>
          </VStack>
        </PanelBody>
      </InspectorControls>
      <div {...useBlockProps({})}>
        <div className="container relative z-20 bg-navy py-10 text-white">
          <div className="flex items-center justify-between">
            <PlaceholderImage
              image={logo}
              className=""
              placeholderClass="w-52 aspect-[4/1]"
              imageClass="h-auto w-52 object-contain"
              onSelect={(media) => {
                setAttributes({
                  logo: {
                    id: media.id,
                    url: media.url
                  }
                });
              }}
              onDelete={() => {
                setAttributes({
                  logo: {
                    id: null,
                    url: null
                  }
                });
              }}
            />

            <div {...innerBlocksProps}>{children}</div>

            <div>
              <SocialLinks className="flex items-center" />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6 text-xs">
              <p>{copyright.replace('[year]', new Date().getFullYear())}</p>
              <ul
                className="flex items-center gap-6"
                dangerouslySetInnerHTML={{
                  __html: legalNavObject?.content.rendered
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

import { __ } from '@wordpress/i18n';
import {
  useBlockProps,
  useInnerBlocksProps,
  InspectorControls,
} from '@wordpress/block-editor';

import {
  PanelBody,
  __experimentalHeading as Heading,
  __experimentalVStack as VStack,
  __experimentalToggleGroupControl as ToggleGroupControl,
  __experimentalToggleGroupControlOption as ToggleGroupControlOption,
} from '@wordpress/components';

import { _class } from '../components/_utils';
import BlockHeader from '../components/BlockHeader';

export default function FeaturedImage({ attributes, setAttributes }) {
  const { image, bgColor } = attributes;

  const { children, ...innerBlocksProps } = useInnerBlocksProps({
    className: 'flex justify-between'
  });

  return (
    <>
      <div
        {...useBlockProps({
          className: _class({
            default: 'container relative flex min-h-[713px] flex-col gap-y-12 px-16 py-16',
            'bg-lightGray': bgColor === 'light-grey',
            'bg-gray': bgColor === 'grey',
            'bg-gradient-navy-to-light-blue text-white': bgColor === 'gradient',
          }),

        })}
      >
        <InspectorControls>
         <PanelBody title="Media Content Settings">
          <VStack spacing={1}>
            <ToggleGroupControl
              label="Background Color"
              value={bgColor}
              isBlock
              __nextHasNoMarginBottom
              __next40pxDefaultSize
              onChange={(value) => setAttributes({ bgColor: value })}
            >
              <ToggleGroupControlOption value="light-grey" label="Light Grey" />
              <ToggleGroupControlOption value="grey" label="Grey" />
              <ToggleGroupControlOption value="gradient" label="Gradient" />
            </ToggleGroupControl>
          </VStack>
        </PanelBody>
        </InspectorControls>
        <BlockHeader
          header={attributes.header}
          setAttributes={setAttributes}
          children={children}
          background={bgColor === 'gradient' ? 'bg-navy' : undefined}
        />

        {image && (
          <figure className="has-circle w-full rounded">
            <img
              src={image.url}
              alt={image.alt}
              className="max-h-[400px] min-h-[300px] w-full object-cover"
            />
          </figure>
        )}
      </div>
    </>
  );
}

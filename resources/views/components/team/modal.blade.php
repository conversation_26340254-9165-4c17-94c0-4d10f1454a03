@props([
    'teamMember' => null,
])

<div
    class="fixed inset-0 z-50 flex max-h-screen items-center justify-center p-4"
    x-bind:id="$id('profile')"
    x-trap.inert.noscroll="open"
    x-show="open"
>
    <div
        x-show="open"
        x-transition.opacity
        class="absolute inset-0 bg-black/80"
        @click="open = false"
    ></div>
    <div
        x-show="open"
        x-transition
        class="relative max-h-[80vh] max-w-5xl overflow-auto bg-lightGray max-lg:max-h-full"
    >
        <button
            x-on:click="open = false"
            class="absolute right-0 top-0 bg-teal p-1.5 transition-colors hover:bg-navy"
        >
            <svg
                width="24"
                height="25"
                viewBox="0 0 24 25"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <g id="ic:baseline-close">
                    <path
                        id="Vector"
                        d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z"
                        fill="white"
                    />
                </g>
            </svg>
        </button>
        <div class="grid-cols-2 gap-x-6 px-6 py-12 max-lg:space-y-6 lg:grid">
            <figure class="space-y-5">
                @unless (empty($teamMember->featured_image))
                    <div>
                        <x-image
                            :image="$teamMember->featured_image"
                            class="aspect-square w-full object-cover object-top"
                        />
                    </div>
                @endunless

                <figcaption>
                    <div class="flex flex-col">
                        @unless (empty($teamMember->name))
                            <h3 class="text-xl font-bold leading-7">
                                {!! $teamMember->name !!}
                            </h3>
                        @endunless

                        @unless (empty($teamMember->position))
                            <p class="text-base font-bold">
                                {!! $teamMember->position->name !!}
                            </p>
                        @endunless

                        @unless (empty($teamMember->location))
                            <p class="my-4 text-base">
                                {!! $teamMember->location !!}
                            </p>
                        @endunless
                    </div>

                    <div class="flex flex-col gap-2">
                        @unless (empty($teamMember->email))
                            <a
                                class="group flex gap-4"
                                href="mailto:{{ $teamMember->email }}"
                            >
                                <x-icons.mail
                                    class="h-6 w-6 flex-none text-navy transition-colors group-hover:text-teal"
                                />

                                <span
                                    class="text-base underline decoration-transparent transition-colors group-hover:decoration-current"
                                >
                                    {!! $teamMember->email !!}
                                </span>
                            </a>
                        @endunless

                        @unless (empty($teamMember->linkedin))
                            <a
                                class="group flex gap-4 transition-colors"
                                href="{{ $teamMember->linkedin }}"
                                target="_blank"
                            >
                                <x-icons.linkedin
                                    class="h-6 w-6 flex-none text-navy transition-colors group-hover:text-teal"
                                />

                                <span
                                    class="text-base underline decoration-transparent transition-colors group-hover:decoration-current"
                                >
                                    {!! $teamMember->linkedin !!}
                                </span>
                            </a>
                        @endunless

                        @unless (empty($teamMember->phone))
                            <a
                                class="flex gap-4"
                                href="tel:{{ $teamMember->phone }}"
                            >
                                <x-icons.phone
                                    class="h-6 w-6 flex-none text-navy transition-colors group-hover:text-teal"
                                />

                                <span
                                    class="text-base underline decoration-transparent transition-colors group-hover:decoration-current"
                                >
                                    {!! $teamMember->phone !!}
                                </span>
                            </a>
                        @endunless
                    </div>
                </figcaption>
            </figure>
            <div>
                <div class="min-h-full lg:h-0 lg:overflow-auto">
                    @unless (empty($teamMember->bio))
                        <div class="prose">
                            {!! $teamMember->bio !!}
                        </div>
                    @endunless
                </div>
            </div>
        </div>
    </div>
</div>

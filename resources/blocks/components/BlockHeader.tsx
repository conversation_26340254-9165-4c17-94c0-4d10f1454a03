import { RichText } from '@wordpress/block-editor';
import { __ } from '@wordpress/i18n';
import Button from './Button';
import { useState } from '@wordpress/element';
import { _class, isDark } from './_utils';
import { Icon } from '@wordpress/components';

type BlockHeaderProps = {
  className?: string;
  header: any;
  setAttributes: (value: any) => void;
  background?: string;
};

export default function BlockHeader({
  className = '',
  header,
  setAttributes,
  background = ''
}: BlockHeaderProps) {
  const { eyebrow, title, introduction, buttons = [] } = header;
  const isDarkBg = isDark(background);

  const updateHeader = (value: object) => {
    setAttributes({
      header: {
        ...header,
        ...value
      }
    });
  };

  const addButton = () => {
    updateHeader({
      buttons: [...buttons, { title: '', url: '', opensInNewTab: false }]
    });
  };

  const updateButton = (index: number, value: any) => {
    const newButtons = [...buttons];
    newButtons[index] = { ...newButtons[index], ...value };
    updateHeader({ buttons: newButtons });
  };

  const [editingButton, setEditingButton] = useState(null);

  return (
    <>
      <div className="flex w-full flex-col border-t border-current pt-3 laptop:flex-row">
        <div className="flex flex-col gap-y-8">
          <div className="flex items-center gap-3">
            <span className="h-5 w-5 rounded-full bg-teal"></span>
            <RichText
              tagName="p"
              className={_class({
                default: 'text-base font-bold uppercase tracking-ultra',
                'text-white': isDarkBg
              })}
              placeholder={__('Eyebrow text...', 'takt')}
              value={eyebrow?.text}
              onChange={(value) =>
                updateHeader({ eyebrow: { ...header.eyebrow, text: value } })
              }
              keepPlaceholderOnFocus
            />
          </div>
        </div>
      </div>

      <div className="flex flex-col gap-y-4 laptop:flex-row">
        <div className="flex w-[412px] shrink-0 gap-x-4">
          <RichText
            tagName="div"
            className={_class({
              default: 'text-4xl',
              'text-navy': !isDarkBg,
              'text-white': isDarkBg
            })}
            placeholder={__('Title...', 'takt')}
            value={title}
            onChange={(value) => {
              updateHeader({ title: value });
            }}
            keepPlaceholderOnFocus
          />
        </div>

        <div className="flex min-w-[300px] flex-col gap-y-6">
          <RichText
            tagName="div"
            className={_class({
              default: 'text-base',
              'text-white': isDarkBg
            })}
            placeholder={__('Text comes here...', 'takt')}
            value={introduction}
            allowedFormats={['core/bold', 'core/italic', 'core/link']}
            onChange={(value) => {
              updateHeader({ introduction: value });
            }}
            keepPlaceholderOnFocus
          />

          <div className="flex gap-4">
            {buttons.length > 0 &&
              buttons.map((button, index) => (
                <div className="group relative" key={button.index}>
                  <button
                    className={_class({
                      default:
                        'absolute right-0 top-0 z-20 flex -translate-y-1/2 translate-x-1/2 content-center justify-items-center rounded-full border border-teal bg-transparent p-1 text-teal opacity-0 transition-opacity hover:bg-teal hover:text-navy group-hover:opacity-100'
                    })}
                    onClick={() => {
                      updateHeader({
                        buttons: buttons.filter(
                          (_, index) => _.index !== button.index
                        )
                      });
                    }}
                  >
                    <Icon className="!m-0" icon="minus" />
                  </button>
                  <Button
                    link={button}
                    variation={button.variation ?? 'primary'}
                    onClick={() => {
                      setEditingButton(button.index);
                    }}
                    showLinkBox={editingButton === button.index}
                    onLinkRemove={() => {
                      updateHeader({
                        buttons: buttons.filter(
                          (_, index) => _.index !== button.index
                        )
                      });
                    }}
                    onChange={(value) => {
                      const _buttons = [...buttons];
                      _buttons[index] = {
                        ..._buttons[index],
                        ...value
                      };
                      if (_buttons[index].index === '') {
                        _buttons[index].index = window.crypto.randomUUID();
                      }
                      updateHeader({
                        buttons: _buttons
                      });
                      setEditingButton(null);
                    }}
                    focusOnMount={button.title.length === 0}
                    onVariationSelect={(value) => {
                      const _buttons = [...buttons];
                      _buttons[index].variation = value;
                      if (_buttons[index].index === '') {
                        _buttons[index].index = window.crypto.randomUUID();
                      }
                      updateHeader({
                        buttons: _buttons
                      });
                    }}
                  />
                </div>
              ))}
            {buttons.length < 1 && (
              <Button
                className={_class({
                  default: '!cursor-pointer opacity-60 hover:opacity-100',
                  'text-white': isDarkBg
                })}
                onClick={() =>
                  updateHeader({
                    buttons: [
                      ...buttons,
                      {
                        url: '',
                        opensInNewTab: false,
                        title: '',
                        variation: 'primary',
                        index: window.crypto.randomUUID()
                      }
                    ]
                  })
                }
              >
                Add Button
              </Button>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

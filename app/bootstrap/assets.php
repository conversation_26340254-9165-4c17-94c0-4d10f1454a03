<?php

namespace App\Bootstrap;

use Ku<PERSON>rut\Vite;

add_action('enqueue_block_assets', function (): void {
    if (! is_admin()) {
        Vite\enqueue_asset(
            get_stylesheet_directory() . '/dist',
            'resources/assets/js/app.ts',
            [
                'css-media' => 'all',
                'handle' => 'takt-app',
            ]
        );

        return;
    }

    Vite\enqueue_asset(
        get_stylesheet_directory() . '/dist',
        'resources/assets/js/editor.ts',
        [
            'handle' => 'takt-editor',
            'dependencies' => ['wp-blocks', 'wp-dom-ready', 'wp-edit-post'],
        ]
    );

    wp_enqueue_style('dashicons');
});

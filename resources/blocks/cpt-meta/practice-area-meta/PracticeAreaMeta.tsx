import { __ } from '@wordpress/i18n';
import {
  useBlockProps,
  RichText,
  useInnerBlocksProps
} from '@wordpress/block-editor';
import { useEntityProp } from '@wordpress/core-data';

import { _class } from '../../components/_utils';
import { Block } from '../../components';

export default function Edit({ attributes, setAttributes, context }) {
  const [meta, updateMeta] = useEntityProp(
    'postType',
    'practice-area',
    'meta',
    context.postId
  );

  const [title, updateTitle] = useEntityProp(
    'postType',
    'practice-area',
    'title',
    context.postId
  );

  const { description, subtitle, content } = meta;

  const innerBlocksProps = useInnerBlocksProps(
    {
      className: 'practiceArea'
    },
    {
      allowedBlocks: ['core/paragraph', 'core/list'],
      templateLock: false
    }
  );

  return (
    <>
      <Block {...useBlockProps()}>
        <div className="flex flex-col gap-y-10">
          <div className="flex flex-1 flex-col gap-8">
            <RichText
              tagName="div"
              className={`text-sm leading-[1.5]`}
              placeholder={__(
                'Please add the area description here...',
                'takt'
              )}
              value={description}
              allowedFormats={['core/bold', 'core/italic']}
              onChange={(value) => {
                updateMeta({ description: value });
              }}
              keepPlaceholderOnFocus
              multiline="p"
            />
          </div>
          <div className="flex gap-x-14 gap-y-4 rounded border p-6">
            <RichText
              tagName="div"
              className={`flex-shrink-0 text-base font-bold`}
              placeholder={__('Subtitle comes here...', '')}
              value={subtitle}
              allowedFormats={[]}
              onChange={(value) => {
                updateMeta({
                  subtitle: value
                });
              }}
              keepPlaceholderOnFocus
              disableLineBreaks={true}
            />

            <div {...innerBlocksProps}>{innerBlocksProps.children}</div>
          </div>
        </div>
      </Block>
    </>
  );
}

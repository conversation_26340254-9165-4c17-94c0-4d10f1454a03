{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "takt/page-header", "version": "0.1.0", "title": "<PERSON> Header", "category": "custom", "icon": "button", "description": "", "keywords": [], "supports": {"html": false, "image": {"dependsOn": [["mediaType", "image"]]}, "multiple": false, "spacing": {"padding": ["top", "bottom"]}}, "attributes": {"title": {"type": "string", "default": ""}, "hasBreadcrumbs": {"type": "boolean", "default": false}, "mediaType": {"type": "string", "default": "image"}, "videoSource": {"type": "string", "default": "youtube", "enum": ["youtube", "vimeo", "local"]}, "video": {"type": "object", "default": {"src": "", "id": 0}}, "videoUrl": {"type": "string", "default": ""}, "cardSize": {"type": "string", "default": "small", "enum": ["small", "large"]}, "style": {"type": "object", "default": {"spacing": {"padding": {"top": "0rem", "bottom": "0rem"}}}}}, "textdomain": "takt", "editorScript": "file:./index.js"}
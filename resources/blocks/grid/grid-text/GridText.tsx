import { __ } from '@wordpress/i18n';

import { RichText, useBlockProps } from '@wordpress/block-editor';

import { _class } from '../../components/_utils';

export default function Edit({ attributes, setAttributes }) {
  const { eyebrow, title, description } = attributes;

  return (
    <>
      <div className="has-circle flex flex-col gap-y-4 bg-white p-8">
        <div className="flex flex-col gap-2">
          <RichText
            tagName="p"
            className="text-base font-bold uppercase tracking-ultra text-black"
            placeholder={__('Eyebrow text...', 'takt')}
            value={eyebrow}
            allowedFormats={[]}
            onChange={(value) => setAttributes({ eyebrow: value })}
            keepPlaceholderOnFocus
            disableLineBreaks={true}
          />
          <RichText
            tagName="p"
            className="text-xl font-bold leading-9 text-navy"
            placeholder={__('Title...', 'takt')}
            value={title}
            allowedFormats={[]}
            onChange={(value) => setAttributes({ title: value })}
            keepPlaceholderOnFocus
            disableLineBreaks={true}
          />
        </div>
        <div className="flex flex-col gap-y-4">
          <RichText
            tagName="p"
            placeholder={__('Description...', 'takt')}
            value={description}
            allowedFormats={[]}
            onChange={(value) => setAttributes({ description: value })}
            keepPlaceholderOnFocus
          />
        </div>
      </div>
    </>
  );
}

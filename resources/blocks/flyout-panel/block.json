{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "takt/flyout-panel", "version": "0.1.0", "title": "Flyout Panel", "category": "custom", "icon": "button", "description": "", "keywords": [], "supports": {"header": true, "html": false, "spacing": {"padding": ["top", "bottom"]}}, "attributes": {"title": {"type": "string", "default": ""}, "text": {"type": "string", "default": ""}, "style": {"type": "object", "default": {"spacing": {"padding": {"top": "var:preset|spacing|small", "bottom": "var:preset|spacing|small"}}}}}, "example": {"attributes": {"header": {"title": "Flyout Panel", "introduction": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat.", "eyebrow": "Eyebrow Text"}}, "innerBlocks": [{"name": "takt/flyout-panel-item", "attributes": {"title": "Panel Title"}, "innerBlocks": [{"name": "takt/media-card", "attributes": {"title": "Media Card Title", "image": {"id": 0, "src": "https://picsum.photos/id/122/600/400"}}}]}, {"name": "takt/flyout-panel-item", "attributes": {"title": "Panel Title"}, "innerBlocks": [{"name": "core/paragraph", "attributes": {"content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat."}}]}, {"name": "takt/flyout-panel-item", "attributes": {"title": "Panel Title"}, "innerBlocks": [{"name": "core/paragraph", "attributes": {"content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat."}}]}]}, "textdomain": "takt", "editorScript": "file:./index.js"}
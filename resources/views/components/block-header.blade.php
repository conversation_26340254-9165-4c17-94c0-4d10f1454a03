@props([
    'header' => [
        'eyebrow' => '',
        'title' => '',
        'introduction' => '',
    ],
    'background' => '',
])

<div
    {{
        $attributes->class([
            'relative flex flex-col gap-y-12 lg:gap-y-16',
            'pt-0' => empty($header['eyebrow']['text']),
        ])
    }}
>
    @unless (empty($header['eyebrow']['text']))
        <div
            class="flex w-full flex-col border-t border-current pt-3 laptop:flex-row"
        >
            <div class="flex items-start gap-3">
                <span class="h-5 w-5 flex-none rounded-full bg-teal"></span>
                <p class="text-base font-bold uppercase tracking-ultra">
                    {!! $header['eyebrow']['text'] !!}
                </p>
            </div>
        </div>
    @endunless

    <div class="flex flex-col gap-x-4 gap-y-4 laptop:flex-row">
        @unless (empty($header['title']) || empty($header['introduction']) || empty($slot))
            <div class="flex w-full shrink-0 gap-x-4 laptop:w-1/3">
                <h2
                    class="{{ $background === 'bg-navy' ? 'text-white' : 'text-navy' }} text-3xl tablet:text-4xl"
                >
                    {!! $header['title'] !!}
                </h2>
            </div>
        @else
            <div class="flex w-full shrink-0 gap-x-4">
                <h2 class="text-3xl text-navy tablet:text-4xl">
                    {!! $header['title'] !!}
                </h2>
            </div>
        @endunless

        @unless (empty($header['introduction']) || empty($slot))
            <div
                class="flex w-full min-w-[min(100%,300px)] flex-col gap-y-6 laptop:w-2/3"
            >
                @unless (empty($header['introduction']))
                    <p class="text-base">
                        {!! $header['introduction'] !!}
                    </p>
                @endunless

                @unless (empty($header['buttons']))
                    <div class="flex flex-wrap gap-x-4 gap-y-2">
                        @foreach ($header['buttons'] as $button)
                            @unless (empty($button['title']))
                                <x-button
                                    class="{{ match($button['variation'] ?? 'primary') {
                                    'inverted' => 'btn-inverted',
                                    'primary' => 'btn-primary',
                                    'secondary' => 'btn-secondary',
                                    'tertiary' => 'btn-tertiary',
                                } }}"
                                    :href="is_string($button['url']) ? $button['url'] : ''"
                                    :target="$button['opensInNewTab'] ? '_blank' : null"
                                >
                                    <span>{{ $button['title'] }}</span>
                                    <span>
                                        <x-icons.squareArrow
                                            class="ml-2 h-4 w-4"
                                        />
                                    </span>
                                </x-button>
                            @endunless
                        @endforeach
                    </div>
                @endunless
            </div>
        @endunless
    </div>
</div>

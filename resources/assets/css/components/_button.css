@layer components {
  /* Create a base button component that both .btn and nav can use */
  .button-base {
    @apply w-fit cursor-pointer text-nowrap rounded-full px-6 py-3 text-base uppercase no-underline transition-all duration-300 hover:no-underline md:px-8 md:py-4;
  }

  .button-primary-base {
    @apply bg-navy text-white;
  }

  /* Use the base components */
  .btn {
    @apply button-base;

    span {
      text-wrap: wrap;
    }
  }

  .btn-primary {
    @apply button-primary-base hover:bg-[#003E6B];
  }

  .btn-inverted {
    @apply bg-white text-navy transition-all duration-300 hover:bg-[#cccccc] hover:drop-shadow-inverted-hover;
  }

  .btn-secondary {
    @apply border border-black bg-transparent hover:border-white hover:bg-white hover:text-navy;
  }

  .btn-tertiary {
    @apply flex h-[28px] items-baseline rounded-none border-b border-b-transparent bg-transparent p-0 no-underline hover:border-b-current hover:text-navy;

    span {
      @apply first:pr-2;
    }
  }

  :is(.btn-primary, .btn-inverted, .btn-secondary) {
    @apply flex items-center justify-center gap-x-4;
  }
}

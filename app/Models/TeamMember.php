<?php

namespace App\Models;

use App\Models\WPBasePost;
use App\Models\Taxonomy;
use Illuminate\Support\Str;

class TeamMember extends WPBasePost
{
    public function getName()
    {
        return $this->title;
    }

    public function getPosition()
    {
        $positions = get_the_terms($this->ID, 'position');

        if (empty($positions)) {
            return false;
        }

        return new Taxonomy($positions[0]);
    }

    public function getShortBio()
    {
        return get_the_excerpt($this->ID);
    }

    public function getBio()
    {
        $block = parse_blocks($this->post_content)[0];

        if (! $block) {
            return '';
        }

        $content = '';

        foreach ($block['innerBlocks'] as $child) {
            $content .= render_block($child);
        }

        if (trim($content) === '<p></p>') {
            return false;
        }

        return $content;
    }
}

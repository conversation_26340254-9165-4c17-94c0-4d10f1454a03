import Swiper from 'swiper';
import {
  Autoplay,
  EffectCoverflow,
  EffectFade,
  Navigation,
  Pagination,
  Parallax,
  Scrollbar
} from 'swiper/modules';
import 'swiper/css';

export default (config = {}) => ({
  swiper: null,

  hasDuplicateSlides: false,

  async init() {
    const SwiperDefaultOptions = {
      modules: [
        Navigation,
        Pagination,
        Parallax,
        Autoplay,
        EffectFade,
        Scrollbar,
        EffectCoverflow
      ],
      navigation: {
        prevEl: this.$refs.prev,
        nextEl: this.$refs.next
      },
      slidesPerView: 'auto',
      spaceBetween: 30,
      loop: true
    };

    this.swiper = new Swiper(this.$el.querySelector('.swiper'), {
      ...SwiperDefaultOptions,
      ...config
    });
  }
});

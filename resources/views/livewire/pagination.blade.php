@if ($pagination->hasPages())
    <ul class="flex items-stretch justify-center">
        @foreach ($pagination->onEachSide(4)->linkCollection() as $pageLink)
            @if ($loop->first)
                <li class="flex-1">
                    <button
                        class="relative ml-auto flex h-full items-center p-4 text-black/25 transition-colors after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-full after:origin-bottom after:bg-black/25 after:transition hover:text-black hover:after:scale-y-[2] hover:after:bg-teal disabled:invisible disabled:opacity-0 lg:p-6"
                        wire:click="goToPage($wire.page - 1)"
                        x-bind:disabled="@js($pagination->onFirstPage())"
                    >
                        <span class="sr-only">previous page</span>
                        <x-icons.chevron class="size-4 rotate-90" />
                    </button>
                </li>
            @elseif ($loop->last)
                <li class="flex-1">
                    <button
                        @class([
                            'relative flex h-full items-center p-4 text-black/25 transition-colors after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-full after:origin-bottom after:bg-black/25 after:transition hover:text-black hover:after:scale-y-[2] hover:after:bg-teal disabled:invisible disabled:opacity-0 lg:p-6',
                        ])
                        wire:click="goToPage($wire.page + 1)"
                        x-bind:disabled="@js($pagination->onLastPage())"
                    >
                        <span class="sr-only">previous page</span>
                        <x-icons.chevron class="size-4 -rotate-90" />
                    </button>
                </li>
            @elseif (empty($pageLink['url']))
                <li class="ellipsis">
                    <span
                        class="relative p-4 after:absolute after:h-0.5 after:w-full after:bg-black/50 disabled:hidden lg:p-6"
                    >
                        {{ $pageLink['label'] }}
                    </span>
                </li>
            @else
                <li
                    @class([
                        'active' => $pageLink['active'],
                        'hidden [.ellipsis~&]:inline' =>
                            $loop->iteration > $pagination->currentPage() + 4,
                    ])
                >
                    <button
                        @class([
                            'relative p-4 lg:p-6 transition-colors hover:text-black after:origin-bottom after:absolute after:bottom-0 after:left-0 after:w-full after:transition hover:after:bg-teal ',
                            'text-black after:h-1 after:bg-teal' => $pageLink['active'],
                            'text-black/50 after:h-0.5 after:bg-black/25 hover:after:scale-y-[2]' => ! $pageLink[
                                'active'
                            ],
                        ])
                        wire:click="goToPage({{ Str::after($pageLink['url'], '=') }})"
                        @disabled($pageLink['active'])
                    >
                        <span class="sr-only">page</span>
                        {{ $pageLink['label'] }}
                    </button>
                </li>
            @endif
        @endforeach
    </ul>
@endif

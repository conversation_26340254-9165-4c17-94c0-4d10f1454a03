import { __ } from '@wordpress/i18n';

import {
  RichText,
  useBlockProps,
  InspectorControls
} from '@wordpress/block-editor';
import {
  PanelBody,
  __experimentalToggleGroupControl as ToggleGroupControl,
  __experimentalToggleGroupControlOption as ToggleGroupControlOption
} from '@wordpress/components';

import { _class } from '../../components/_utils';

export default function Edit({ attributes, setAttributes }) {
  const { value, description, type } = attributes;

  return (
    <>
      <InspectorControls>
        <PanelBody title="Stats Settings">
          <ToggleGroupControl
            label="Type"
            value={type}
            onChange={(value) => setAttributes({ type: value })}
          >
            <ToggleGroupControlOption value="number" label="Number" />
            <ToggleGroupControlOption value="string" label="String" />
          </ToggleGroupControl>
        </PanelBody>
      </InspectorControls>
      <div {...useBlockProps()}>
        <div className="flex flex-col gap-2 border-t border-current py-2">
          <div className="flex">
            <RichText
              tagName="div"
              className={_class({
                default: 'transition-all',
                'text-5xl': type === 'number',
                'text-xl': type === 'string'
              })}
              placeholder={__('Value', 'takt')}
              value={value}
              allowedFormats={[]}
              onChange={(value) => setAttributes({ value: value })}
              keepPlaceholderOnFocus
              disableLineBreaks={true}
            />
          </div>

          <RichText
            tagName="div"
            className="text-sm"
            placeholder={__('Description', 'takt')}
            value={description}
            allowedFormats={[]}
            onChange={(value) => setAttributes({ description: value })}
            keepPlaceholderOnFocus
          />
        </div>
      </div>
    </>
  );
}

{"name": "takt/starter-theme", "type": "wordpress-theme", "license": "MIT", "description": "WordPress starter theme with a modern development workflow", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "keywords": ["wordpress"], "support": {"issues": "https://github.com/roots/sage/issues", "forum": "https://discourse.roots.io/"}, "autoload": {"psr-4": {"App\\": "app/"}}, "repositories": [{"type": "vcs", "url": "https://github.com/ssang/wp-create-block.git", "branch": "master"}], "require": {"php": "^8.0", "kucrut/vite-for-wp": "^0.10.0", "livewire/livewire": "^3.6", "log1x/acf-composer": "^3.0.23", "log1x/pagi": "^1.1", "roots/acorn": "^4.3.0", "roots/acorn-fse-helper": "^1.0"}, "require-dev": {"squizlabs/php_codesniffer": "3.7.1", "takt/create-block": "dev-master"}, "suggest": {"log1x/sage-directives": "A collection of useful Blade directives for WordPress and Sage (^1.0).", "log1x/sage-svg": "A useful SVG directive for inlining SVG's within Blade views (^1.0)."}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"lint": ["phpcs --extensions=php --standard=PSR12 app"], "post-autoload-dump": ["Roots\\Acorn\\ComposerScripts::postAutoloadDump"]}, "extra": {"acorn": {"providers": ["App\\Providers\\ThemeServiceProvider"]}}}
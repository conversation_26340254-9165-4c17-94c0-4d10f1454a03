{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "takt/grid", "version": "0.1.0", "title": "Grid", "category": "custom", "icon": "button", "description": "", "keywords": [], "supports": {"html": false, "header": true, "spacing": {"padding": ["top", "bottom"]}}, "attributes": {"columns": {"type": "string", "default": "3", "enum": ["3", "4"]}, "style": {"type": "object", "default": {"spacing": {"padding": {"top": "var:preset|spacing|small", "bottom": "var:preset|spacing|small"}}}}}, "example": {"attributes": {"header": {"title": "Grid", "introduction": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat.", "eyebrow": "Eyebrow Text"}, "columns": 3}, "innerBlocks": [{"name": "takt/grid-contact", "attributes": {"eyebrow": "Eyebrow", "location": "Canada", "address": "123 Fake St", "phone1": "************", "email": "<EMAIL>"}}, {"name": "takt/grid-contact", "attributes": {"eyebrow": "Eyebrow", "location": "Canada", "address": "123 Fake St", "phone1": "************", "email": "<EMAIL>"}}, {"name": "takt/grid-text", "attributes": {"title": "Grid Item Title", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat.", "eyebrow": "Eyebrow"}}]}, "textdomain": "takt", "editorScript": "file:./index.js"}
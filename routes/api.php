<?php

use App\Http\Middleware\WPAuthenticate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::prefix('takt/v1')->group(function () {
    Route::middleware(WPAuthenticate::class)->group(function () {
        Route::get('/form/{id}', function (Request $request) {
            return view('components.gform')->with(['form' => $request['id']]);
        })->whereNumber('id');
    });

    Route::get('job-listing', function (Request $request) {
        $city = $request->query('city');
        $province = $request->query('province');
        $search = $request->query('search');
        $page = $request->query('page', 1);
        $perPage = $request->query('perPage', 10);
        $sort = $request->query('sort', 'date');

        $orderby = 'date';
        $order = 'DESC';

        if ($sort === 'title') {
            $orderby = 'title';
            $order = 'ASC';
        }

        $args = [
            'post_type' => 'job-listing',
            'post_status' => 'publish',
            'posts_per_page' => $perPage,
            'paged' => $page,
            'orderby' => $orderby,
            'order' => $order,
        ];

        if ($search) {
            $args['s'] = $search;
        }

        $tax_query = [];

        if ($city) {
            $tax_query[] = [
                'taxonomy' => 'city',
                'field' => 'slug',
                'terms' => $city,
            ];
        }

        if ($province) {
            $tax_query[] = [
                'taxonomy' => 'province',
                'field' => 'slug',
                'terms' => $province,
            ];
        }

        if (!empty($tax_query)) {
            $args['tax_query'] = $tax_query;
        }

        $query = new WP_Query($args);
        $jobs = [];
        $total_count = $query->found_posts;

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $jobs[] = [
                    'id' => get_the_ID(),
                    'title' => get_the_title(),
                    'company' => get_post_meta(get_the_ID(), 'company', true),
                    'excerpt' => get_the_excerpt(),
                    'link' => get_permalink(),
                    'job_id' => get_post_meta(get_the_ID(), 'job_id', true),
                    'job_details' => get_post_meta(get_the_ID(), 'job_details', true),
                    'logo' => get_the_post_thumbnail_url(),
                    'city_taxonomy' => array_map(function ($term) {
                        return [
                            'id' => $term->term_id,
                            'title' => $term->name,
                        ];
                    }, is_wp_error(wp_get_post_terms(get_the_ID(), 'city')) ? [] : wp_get_post_terms(get_the_ID(), 'city')),
                    'province_taxonomy' => array_map(function ($term) {
                        return [
                            'id' => $term->term_id,
                            'title' => get_term_meta($term->term_id, 'takt-job-province-code', true) ?: $term->name,
                        ];
                    }, is_wp_error(wp_get_post_terms(get_the_ID(), 'province')) ? [] : wp_get_post_terms(get_the_ID(), 'province')),
                ];
            }
            wp_reset_postdata();
        }

        return response()->json([
            'total_count' => $total_count,
            'jobs' => $jobs,
        ]);
    });

    Route::get('posts', function (Request $request) {
        $category = $request->query('category');
        $search = $request->query('search');
        $page = $request->query('page', 1);
        $perPage = $request->query('perPage', 10);
        $sort = $request->query('sort', 'date');

        $orderby = 'date';
        $order = 'DESC';

        if ($sort === 'date_asc') {
            $orderby = 'date';
            $order = 'ASC';
        } elseif ($sort === 'title') {
            $orderby = 'title';
            $order = 'ASC';
        } elseif ($sort === 'title_desc') {
            $orderby = 'title';
            $order = 'DESC';
        }

        $args = [
            'post_type' => 'post',
            'post_status' => 'publish',
            'posts_per_page' => $perPage,
            'paged' => $page,
            'orderby' => $orderby,
            'order' => $order,
        ];

        if ($search) {
            $args['s'] = $search;
        }

        if ($category) {
            $args['category_name'] = $category;
        }

        $query = new WP_Query($args);
        $posts = [];
        $total_count = $query->found_posts;

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $posts[] = [
                    'id' => get_the_ID(),
                    'title' => get_the_title(),
                    'excerpt' => get_the_excerpt(),
                    'link' => get_permalink(),
                    'date' => get_the_date('F j, Y'),
                    'featured_image' => get_the_post_thumbnail_url(get_the_ID(), 'medium'),
                    'categories' => array_map(function ($category) {
                        return [
                            'id' => $category->term_id,
                            'name' => $category->name,
                            'slug' => $category->slug,
                        ];
                    }, get_the_category()),
                ];
            }
            wp_reset_postdata();
        }

        return response()->json([
            'total_count' => $total_count,
            'posts' => $posts,
        ]);
    });
});

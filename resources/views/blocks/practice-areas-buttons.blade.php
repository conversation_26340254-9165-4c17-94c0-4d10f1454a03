<x-block :$block class="container gap-y-14 lg:gap-y-16">
    <x-block-header :$header class="pb-0 pt-0" />

    @php
        $args = [
            'post_type' => 'practice-area',
            'posts_per_page' => -1,
            'post_status' => 'publish',
            'orderBy' => 'title',
            'order' => 'ASC',
        ];
        $practice_areas = new WP_Query($args);
    @endphp

    @if ($practice_areas->have_posts())
        <div class="flex flex-col gap-4 laptop:grid laptop:grid-cols-3">
            @while ($practice_areas->have_posts())
                @php
                    $practice_areas->the_post();
                    $description = get_post_meta(get_the_ID(), 'description', true);
                    $subtitle = get_post_meta(get_the_ID(), 'subtitle', true);
                    $content = get_the_content();
                    $post_name = get_post_field('post_name', get_post());
                    $page_permalink = get_page_link($pageId ?? 0);
                @endphp

                <a
                    href="{{ $attributes['linksDestination'] === 'external-page' ? $page_permalink ?? '' : '' }}#{{ $post_name }}"
                    class="practiceAreaButton group/area relative h-auto w-full overflow-hidden rounded border p-6 transition-colors hover:border-teal laptop:min-h-[138px]"
                >
                    <div
                        class="absolute inset-0 opacity-0 transition-opacity duration-300 before:absolute before:inset-0 before:bg-teal/85 group-hover/area:opacity-100"
                    >
                        @if (has_post_thumbnail())
                            {{ the_post_thumbnail('medium', ['class' => 'h-full w-full object-cover']) }}
                        @endif
                    </div>
                    <div
                        class="relative z-10 flex h-full w-full items-end justify-between gap-2"
                    >
                        <h2
                            class="relative h-auto w-full text-lg font-bold capitalize leading-9 text-navy decoration-transparent transition-colors group-hover/area:text-white group-hover/area:decoration-current md:text-xl"
                        >
                            {!! get_the_title() !!}
                        </h2>
                        <svg
                            class="z-[1] self-center"
                            width="54"
                            height="54"
                            viewBox="0 0 54 54"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                class="group-hover/area:stroke-white"
                                d="M1.59082 52.4094L51.7757 2.22447M51.7757 2.22447L51.3018 49.1474M51.7757 2.22447L4.85284 2.69843"
                                stroke="#004D86"
                                stroke-width="3"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                        </svg>
                    </div>
                </a>
            @endwhile
        </div>

        @php
            wp_reset_postdata();
        @endphp
    @else
        <p>{{ __('No practice areas found.', 'takt') }}</p>
    @endif
</x-block>

import { __ } from '@wordpress/i18n';
import {
  useBlockProps,
  useInnerBlocksProps,
  InspectorControls
} from '@wordpress/block-editor';

import { _class } from '../components/_utils';
import BlockHeader from '../components/BlockHeader';
import PreviewImage from './PracticeAreasCarouselPreview.jpg';

import PostSingleSelect from '../components/PostSingleSelect';

import {
  PanelBody,
  ToggleControl,
  __experimentalToggleGroupControl as ToggleGroupControl,
  __experimentalToggleGroupControlOption as ToggleGroupControlOption
} from '@wordpress/components';

export default function PracticeAreasCarousel({ attributes, setAttributes }) {
  const { enablePagination, enableNavigation, linksDestination, pageId } =
    attributes;

  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    {
      className: `flex justify-between`
    },
    {
      allowedBlocks: ['takt/logo'],
      template: [['takt/logo']],
      renderAppender: false
    }
  );

  return (
    <>
      <InspectorControls>
        <PanelBody title="Practice Areas Carousel Settings">
          <ToggleGroupControl
            label="Links Destination"
            value={linksDestination}
            onChange={(value) => setAttributes({ linksDestination: value })}
            isBlock
            __nextHasNoMarginBottom
            __next40pxDefaultSize
          >
            <ToggleGroupControlOption value="same-page" label="Same Page" />
            <ToggleGroupControlOption
              value="external-page"
              label="External Page"
            />
          </ToggleGroupControl>

          {linksDestination == 'external-page' && (
            <PostSingleSelect
              label="Select a Post"
              value={attributes.pageId}
              postType="page"
              onSelect={(value) => setAttributes({ pageId: value })}
            />
          )}

          <ToggleControl
            label="Enable Pagination"
            value={enablePagination}
            onChange={(value) => setAttributes({ enablePagination: value })}
          />

          <ToggleControl
            label="Enable Navigation"
            value={enableNavigation}
            onChange={(value) => setAttributes({ enableNavigation: value })}
          />
        </PanelBody>
      </InspectorControls>

      <div
        {...useBlockProps()}
        className="container relative flex flex-col gap-y-16 px-16 py-16"
      >
        <BlockHeader setAttributes={setAttributes} header={attributes.header} />

        <div className="relative inset-0 z-10 flex flex-col items-center justify-center border-t pb-48 pt-0">
          <div
            className="absolute inset-0 bg-contain bg-center bg-no-repeat opacity-10"
            style={{ backgroundImage: `url(${PreviewImage})` }}
          ></div>

          <div className="pt-32 text-center">
            <h1 className="font-heading text-5xl">Practice Areas Carousel</h1>
            <p className="text-gray-600 mt-4 text-lg">
              {__(
                'The Practice Areas Carousel is displayed on the front-end.',
                'text-domain'
              )}
            </p>
          </div>
        </div>
      </div>
    </>
  );
}

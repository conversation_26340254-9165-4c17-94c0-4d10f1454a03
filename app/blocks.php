<?php

namespace App;

use App\Models\Block;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\View;

/**
 * Determine what blocks are available in the picker
 */
add_filter(
    'allowed_block_types_all',
    function ($allowedBlocks, $editorContext) {
        $blockTypes = \WP_Block_Type_Registry::get_instance()->get_all_registered();
        $postType = $editorContext->post->post_type ?? 'editor';

        $allowedBlocks = collect(
            array_keys(
                Arr::where($blockTypes, function ($block, $name) use (
                    $postType
                ) {
                    if (Str::before($name, '/') != 'takt') {
                        return false;
                    }

                    if (
                        // in_array($postType, ['page', 'editor']) &&
                        !block_has_support($block, 'postTypes')
                    ) {
                        return true;
                    }

                    return in_array(
                        $postType,
                        $block->supports['postTypes'] ?? []
                    );
                })
            )
        );

        if ($postType === 'post') {
            $allowedBlocks->add('core/image');
        }

        if ($editorContext->name == 'core/edit-site') {
            $allowedBlocks->add('core/navigation');
            $allowedBlocks->add('core/navigation-link');
            $allowedBlocks->add('core/navigation-submenu');
        }

        return array_merge($allowedBlocks->all(), [
            'core/navigation',
            'core/navigation-link',
            'core/paragraph',
            'core/heading',
            'core/quote',
            'core/list',
            'core/list-item',
            'core/block',
            'core/image',
            'takt/button',
            'takt/button-group'
        ]);
    },
    25,
    2
);

/**
 * Add a new category for custom blocks
 */
add_filter('block_categories_all', function ($categories) {
    return array_merge([
        [
            'slug'  => 'custom',
            'title' => 'Theme Blocks'
        ],
        [
            'slug' => 'meta',
            'title' => 'Metadata',
            'description' => 'Just a block to house post metadata'
        ]
    ], $categories);
});

/**
 * Add a new category for block patterns
 */
add_action('init', function () {
    register_block_pattern_category(
        'custom',
        ['label' => __('Takt', 'takt')]
    );
});

/**
 * Register all available custom blocks
 */
add_action('init', function () {

    if (! file_exists($path = get_stylesheet_directory() . '/dist/blocks')) {
        return;
    }

    $blocks = new \FilesystemIterator($path);

    takt_register_all_blocks($blocks);

    if (file_exists($path = get_stylesheet_directory() . '/dist/blocks/cpt-meta')) {
        $metaBlocks = new \FilesystemIterator($path . '/');

        takt_register_all_blocks($metaBlocks);
    }
});

function takt_register_all_blocks($blocks)
{
    foreach ($blocks as $dir) {
        if (! $dir->isDir() || ! file_exists($dir->getPathname() . '/block.json')) {
            continue;
        }

        takt_register_all_blocks(new \FilesystemIterator($dir->getPathname()));

        $block = register_block_type(
            $dir->getPathname(),
            [
                'render_callback' => function ($attributes, $children, $block) {
                    $blockName = Str::after($block->name, '/');

                    try {
                        if (Str::startsWith($blockName, 'site-')) {
                            $section = Str::of($blockName)->after('site-');

                            return View::first(
                                [
                                    $section
                                        ->prepend('sections.')
                                        ->append('-' . ($block->context['postType'] ?? ''))
                                        ->toString(),
                                    $section
                                        ->prepend('sections.')
                                        ->toString(),
                                ],
                                compact('attributes', 'block', 'children')
                            );
                        }

                        $block = new Block($block);

                        return View::first([
                            'content.' . Str::beforeLast($blockName, '-meta'),
                            'blocks.partials.' . $blockName,
                            'blocks.' . $blockName
                        ], compact('attributes', 'block', 'children'));
                    } catch (\Exception $e) {
                        if ($block->block_type->category != 'meta') {
                            return '<span style="text-align: center; font-size: 4rem">View does not exist for ' . $blockName . '</span>';
                        }
                    }
                },
            ]
        );
    }
}

/**
 * Find and import all block meta files
 */
if (file_exists($path = __DIR__ . '/BlockMeta')) {
    $BlockMeta = new \FilesystemIterator($path);

    foreach ($BlockMeta as $meta) {
        if ($meta->isFile()) {
            require_once($meta->getRealPath());
        }
    }
}

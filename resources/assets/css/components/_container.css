@layer components {
  .container {
    --gutter: min(1.5rem, 1.38vw);
    --max-content-size: min(1282 / 1440 * 100%, 112rem);
    --max-wide-size: 120rem;
    --wide-gutter: min(
      4.44444vw,
      (var(--max-wide-size) - var(--max-content-size)) / 2
    );

    display: grid;
    grid-template-columns:
      [full-start] minmax(var(--gutter), 1fr)
      [wide-start] minmax(0, var(--wide-gutter))
      [content-start] min(var(--max-content-size), 100% - var(--gutter) * 2)
      [content-end]
      minmax(0, var(--wide-gutter)) [wide-end]
      minmax(var(--gutter), 1fr) [full-end];

    & > * {
      grid-column: content;

      &.content-wide {
        grid-column: wide;
      }

      &.content-full {
        grid-column: full;
      }
    }

    &.container-wide > * {
      grid-column: wide;
    }

    &.container-full > * {
      grid-column: full;
    }

    .container {
      display: block;
    }
  }
}

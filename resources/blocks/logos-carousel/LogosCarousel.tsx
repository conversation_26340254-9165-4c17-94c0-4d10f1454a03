import { __ } from '@wordpress/i18n';

import {
  useBlockP<PERSON>,
  ButtonBlockAppender,
  useInnerBlocksProps,
  store as blockEditorStore
} from '@wordpress/block-editor';

import { useSelect } from '@wordpress/data';
import { useRef, useEffect } from '@wordpress/element';

import { _class } from '../components/_utils';
import BlockHeader from '../components/BlockHeader';

export default function LogosCarousel({ clientId, attributes, setAttributes }) {
  const sliderRef = useRef(null);

  const blockCount = useSelect((select) => {
    return select(blockEditorStore).getBlockCount(clientId);
  }, []);

  useEffect(() => {
    sliderRef.current?.swiper?.update();
  }, [blockCount]);

  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    {
      className: `flex justify-between`
    },
    {
      allowedBlocks: ['takt/logo'],
      template: [['takt/logo']],
      renderAppender: false
    }
  );

  return (
    <>
      <div
        {...useBlockProps({
          className: _class({
            default:
              'container relative flex min-h-[713px] flex-col gap-y-16 px-16 py-16'
          })
        })}
      >
        <BlockHeader
          header={attributes.header}
          setAttributes={(value) => setAttributes(value)}
          clientId={clientId}
        />

        <div className="relative">
          <swiper-container
            class="flex"
            ref={sliderRef}
            slides-per-view={'auto'}
            space-between={54}
            focusable-elements=".rich-text"
          >
            {children}
          </swiper-container>

          <div className="relative flex justify-end pt-14">
            <div className="absolute -bottom-14 right-[--gutter] flex items-center gap-6 pl-6">
              <button
                onClick={() => sliderRef.current?.swiper?.slidePrev()}
                className="inline-flex aspect-square items-center justify-center rounded-full border border-black px-6 transition-colors hover:bg-black hover:text-white"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="29"
                  height="16"
                  viewBox="0 0 29 16"
                  fill="none"
                >
                  <path
                    d="M0.792891 7.29289C0.402367 7.68342 0.402367 8.31658 0.79289 8.7071L7.15685 15.0711C7.54738 15.4616 8.18054 15.4616 8.57107 15.0711C8.96159 14.6805 8.96159 14.0474 8.57107 13.6569L2.91421 8L8.57107 2.34314C8.96159 1.95262 8.96159 1.31945 8.57107 0.92893C8.18054 0.538406 7.54738 0.538406 7.15685 0.92893L0.792891 7.29289ZM28.5 7L1.5 7L1.5 9L28.5 9L28.5 7Z"
                    fill="currentColor"
                  />
                </svg>
              </button>
              <ButtonBlockAppender
                className="slider-appender has-icon size-16 rounded-full border border-black bg-transparent px-6 text-black transition-colors hover:bg-black hover:text-white"
                rootClientId={clientId}
              />
              <button
                onClick={() => sliderRef.current?.swiper?.slideNext()}
                className="inline-flex aspect-square items-center justify-center rounded-full border border-black px-6 transition-colors hover:bg-black hover:text-white"
              >
                <svg
                  className="rotate-180"
                  xmlns="http://www.w3.org/2000/svg"
                  width="29"
                  height="16"
                  viewBox="0 0 29 16"
                  fill="none"
                >
                  <path
                    d="M0.792891 7.29289C0.402367 7.68342 0.402367 8.31658 0.79289 8.7071L7.15685 15.0711C7.54738 15.4616 8.18054 15.4616 8.57107 15.0711C8.96159 14.6805 8.96159 14.0474 8.57107 13.6569L2.91421 8L8.57107 2.34314C8.96159 1.95262 8.96159 1.31945 8.57107 0.92893C8.18054 0.538406 7.54738 0.538406 7.15685 0.92893L0.792891 7.29289ZM28.5 7L1.5 7L1.5 9L28.5 9L28.5 7Z"
                    fill="currentColor"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

<?php

namespace App\PostTypes;

/**
 * Create Custom Post Type for Job Listings
 */
add_action(
    'init',
    function () {
        $customArgs = [
            'menu_position' => 11,
            'supports' => [
                'editor',
                'title',
                'thumbnail',
                'revisions',
                'custom-fields',
                'excerpt',
            ],
            'has_archive' => 'candidate-opportunities',
            'public' => true,
            'publicly_queryable' => true,
            'show_in_rest' => true,
            'rewrite' => [
                'slug' => 'candidate-opportunities',
                'with_front' => false,
            ],
            'show_in_nav_menus' => true,
            'menu_icon' =>
            'data:image/svg+xml;base64,' .
                base64_encode(
                    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-6"><path fill-rule="evenodd" d="M7.5 5.25a3 3 0 0 1 3-3h3a3 3 0 0 1 3 3v.205c.933.085 1.857.197 2.774.334 1.454.218 2.476 1.483 2.476 2.917v3.033c0 1.211-.734 2.352-1.936 2.752A24.726 24.726 0 0 1 12 15.75c-2.73 0-5.357-.442-7.814-1.259-1.202-.4-1.936-1.541-1.936-2.752V8.706c0-1.434 1.022-2.7 2.476-2.917A48.814 48.814 0 0 1 7.5 5.455V5.25Zm7.5 0v.09a49.488 49.488 0 0 0-6 0v-.09a1.5 1.5 0 0 1 1.5-1.5h3a1.5 1.5 0 0 1 1.5 1.5Zm-3 8.25a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z" clip-rule="evenodd" /><path d="M3 18.4v-2.796a4.3 4.3 0 0 0 .713.31A26.226 26.226 0 0 0 12 17.25c2.892 0 5.68-.468 8.287-1.335.252-.084.49-.189.713-.311V18.4c0 1.452-1.047 2.728-2.523 2.923-2.12.282-4.282.427-6.477.427a49.19 49.19 0 0 1-6.477-.427C4.047 21.128 3 19.852 3 18.4Z" /></svg>'
                ),
            'template_lock' => 'insert',
            'template' => [
                [
                    'takt/job-listing-header',
                    [
                        'lock' => [
                            'remove' => true,
                            'move' => true,
                        ],
                    ]
                ],
                [
                    'takt/job-listing-post',
                    [
                        'lock' => [
                            'remove' => true,
                            'move' => true,
                        ],
                    ]
                ],
                [
                    'takt/cta',
                ],
            ],
        ];

        \new_custom_post_type('job-listing', 'Opportunity', 'Opportunities', $customArgs, 0);

        $taxonomies = [
            'city' => 'City',
            'province' => 'Province',
        ];

        foreach ($taxonomies as $taxonomy => $label) {
            $args = [
                'hierarchical' => true,
                'labels' => [
                    'name' => $label,
                    'singular_name' => $label,
                    'search_items' => 'Search ' . $label,
                    'all_items' => 'All ' . $label,
                    'parent_item' => 'Parent ' . $label,
                    'parent_item_colon' => 'Parent ' . $label . ':',
                    'edit_item' => 'Edit ' . $label,
                    'update_item' => 'Update ' . $label,
                    'add_new_item' => 'Add New ' . $label,
                    'new_item_name' => 'New ' . $label,
                    'menu_name' => $label,
                ],
                'show_ui' => true,
                'show_in_rest' => true,
                'show_in_quick_edit' => true,
                'show_admin_column' => true,
                'rewrite' => [
                    'slug' => $taxonomy,
                    'with_front' => false,
                ],
                // Add the custom rest_base to avoid conflicts
                'rest_base' => $taxonomy . '_taxonomy',
            ];

            // Register each taxonomy
            register_taxonomy($taxonomy, ['job-listing'], $args);
        };

        $postMeta = [
            'job_id',
            'city',
            'province',
            'company'
        ];

        foreach ($postMeta as $key => $customArgs) {
            $args = [
                'show_in_rest' => true,
                'single' => true,
                'type' => 'string',
                'sanitize_callback' => 'wp_kses_post',
            ];

            if (is_numeric($key)) {
                $key = $customArgs;
            }

            if (is_array($customArgs)) {
                $args = array_merge($args, $customArgs);
            }

            register_post_meta('job-listing', $key, $args);
        }
    },
    0
);

$province = get_query_var('province');
$perPage = get_query_var('perPage');

add_filter('query_vars', function ($vars) {
    $vars[] = 'province';
    $vars[] = 'perPage';
    return $vars;
});

add_filter('job-listing', function ($title) {
    $screen = get_current_screen();

    if ($screen->post_type == 'job-listing') {
        $title = 'Opportunities';
    }

    return $title;
});

add_action('province_add_form_fields', function () {
    $field = <<<PROVINCE_CODE_FIELD
    <div class="form-field">
        <label for="takt-job-province-code">Province Code</label>
        <input name="takt-job-province-code" id="takt-job-province-code" type="text" value="">
        <p id="takt-job-province-code-description">Province code to be used when necessary</p>
    </div>
    PROVINCE_CODE_FIELD;

    echo $field;
});

add_action('create_province', 'App\\PostTypes\\takt_save_province_meta');

add_action('province_edit_form_fields', function ($term) {
    $code = get_term_meta($term->term_id, 'takt-job-province-code', true);

    $cell = <<<PROVINCE_CODE_FIELD
    <tr class="form-field">
        <th scope="row"><label for="takt-job-province-code">Province Code</label></th>
        <td><input name="takt-job-province-code" id="takt-job-province-code" type="text" value="$code">
            <p class="description">Province code to be used when necessary</p>
        </td>
    </tr>
    PROVINCE_CODE_FIELD;

    echo $cell;
});

add_action('edited_province', 'App\\PostTypes\\takt_save_province_meta');

function takt_save_province_meta($term_id)
{
    if (!isset($_POST['takt-job-province-code'])) {
        return;
    }

    update_term_meta($term_id, 'takt-job-province-code', $_POST['takt-job-province-code']);
}

/**
 * Add custom title for posts list that combines company and job title
 */
add_filter(
    'manage_job-listing_posts_columns',
    function ($posts_columns) {
        $posts_columns['title'] = 'Position - Company';

        return $posts_columns;
    }
);

add_action(
    'admin_head-edit.php',
    function () {
        add_filter(
            'the_title',
            function ($title, $id) {
                $suffix = '';

                if (get_post_meta($id, 'company', true)) {
                    $suffix .= ' - ' . get_post_meta($id, 'company', true);
                }

                return $title . $suffix;
            },
            100,
            2
        );
    }
);

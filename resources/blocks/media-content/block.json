{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "takt/media-content", "version": "0.1.0", "title": "Media Content", "category": "custom", "icon": "button", "description": "", "keywords": [], "supports": {"html": false, "image": {"dependsOn": [["mediaType", "image"]], "imagePosition": true}, "spacing": {"padding": ["top", "bottom"]}}, "attributes": {"eyebrow": {"type": "string", "default": ""}, "title": {"type": "string", "default": ""}, "description": {"type": "string", "default": ""}, "layout": {"type": "string", "default": "fifty-fifty", "enum": ["fifty-fifty", "thirty-seventy"]}, "mediaType": {"type": "string", "default": "image"}, "mediaPosition": {"type": "string", "default": "right", "enum": ["left", "right"]}, "videoSource": {"type": "string", "default": "youtube", "enum": ["youtube", "vimeo", "local"]}, "video": {"type": "object", "default": {"src": "", "id": 0}}, "videoUrl": {"type": "string", "default": ""}, "style": {"type": "object", "default": {"spacing": {"padding": {"top": "var:preset|spacing|small", "bottom": "var:preset|spacing|small"}}}}, "hasBgGraphic": {"type": "boolean", "default": false}}, "example": {"attributes": {"eyebrow": "Eyebrow Text", "title": "Media Content", "image": {"url": "https://images.unsplash.com/photo-1554629947-334ff61d85dc?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=774&q=80", "id": 0}}, "innerBlocks": [{"name": "core/paragraph", "attributes": {"content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat."}}, {"name": "takt/button", "attributes": {"title": "Button Title", "url": "https://google.com", "opensInNewTab": false, "type": "primary"}}]}, "textdomain": "takt", "editorScript": "file:./index.js"}
{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "takt/forms", "version": "0.1.0", "title": "Forms", "category": "custom", "icon": "button", "description": "", "keywords": [], "supports": {"html": false, "spacing": {"padding": ["top", "bottom"]}}, "attributes": {"eyebrow": {"type": "string", "default": ""}, "title": {"type": "string", "default": ""}, "description": {"type": "string", "default": ""}, "formType": {"type": "string", "default": "gravity-forms", "enum": ["page-redirect", "gravity-forms"]}, "formId": {"type": "integer", "default": null}, "style": {"type": "object", "default": {"spacing": {"padding": {"top": "var:preset|spacing|small", "bottom": "var:preset|spacing|small"}}}}}, "example": {"attributes": {"eyebrow": "Eyebrow Text", "title": "Forms", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat.", "formId": 2}}, "textdomain": "takt", "editorScript": "file:./index.js"}
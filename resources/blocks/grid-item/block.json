{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "takt/grid-item", "version": "0.1.0", "title": "<PERSON><PERSON>", "category": "custom", "icon": "button", "description": "", "keywords": [], "supports": {"html": false, "header": true, "image": true, "spacing": {"padding": ["top", "bottom"]}}, "attributes": {"backgroundType": {"type": "string", "default": "image", "enum": ["image", "gradient"]}, "headerType": {"type": "string", "default": "default", "enum": ["default", "green"]}, "eyebrow": {"type": "string", "default": ""}, "title": {"type": "string", "default": ""}, "description": {"type": "string", "default": ""}, "columns": {"type": "string", "default": "3", "enum": ["3", "4"]}, "style": {"type": "object", "default": {"spacing": {"padding": {"top": "", "bottom": "var:preset|spacing|small"}}}}}, "example": {"attributes": {"header": {"title": "Grid", "introduction": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat.", "eyebrow": "Eyebrow Text"}, "columns": 3}, "innerBlocks": [{"name": "takt/contact-card", "attributes": {"eyebrow": "Eyebrow", "location": "Canada", "address": "123 Fake St", "phone1": "************", "email": "<EMAIL>"}}, {"name": "takt/contact-card", "attributes": {"eyebrow": "Eyebrow", "location": "Canada", "address": "123 Fake St", "phone1": "************", "email": "<EMAIL>"}}, {"name": "takt/contact-card", "attributes": {"eyebrow": "Eyebrow", "location": "Canada", "address": "123 Fake St", "phone1": "************", "email": "<EMAIL>"}}]}, "textdomain": "takt", "editorScript": "file:./index.js"}
const plugin = require('tailwindcss/plugin');

module.exports = {
  content: [
    './index.php',
    './app/**/*.php',
    './resources/**/*.{php,vue,js,ts,tsx}',
    '!./resources/blocks/*.js',
    './config/acf.php'
  ],
  corePlugins: {
    container: false
  },
  theme: {
    fontFamily: {
      sans: ['Arial', 'sans-serif']
    },
    fontSize: {
      heading: [
        '5.25rem',
        {
          lineHeight: '1.2'
        }
      ],
      '5xl': [
        '5.25rem',
        {
          lineHeight: '1.2'
        }
      ],
      '4xl': [
        '3.375rem',
        {
          lineHeight: '1.2'
        }
      ],
      '3xl': [
        '2.625rem',
        {
          lineHeight: '1.2'
        }
      ],
      '2xl': [
        '2rem',
        {
          lineHeight: '1.5'
        }
      ],
      xl: [
        '1.5rem',
        {
          lineHeight: '1.5'
        }
      ],
      lg: [
        '1.25rem',
        {
          lineHeight: '1.5'
        }
      ],
      base: [
        '1.125rem',
        {
          lineHeight: '1.5'
        }
      ],
      sm: [
        '1rem',
        {
          lineHeight: '1.5'
        }
      ],
      xs: [
        '0.875rem',
        {
          lineHeight: '1.5'
        }
      ]
    },
    extend: {
      colors: {
        teal: '#17C2C8',
        navy: '#004D86',
        gray: '#C8D6E1',
        lightGray: '#EDEFF3',
        darkGray: '#2B2B2B',
        black: '#2b2b2b'
      },
      spacing: {
        '6': '1.5rem'
      },

      backgroundImage: {
        radial: "url('@/assets/images/radial.svg')",
        'gradient-radial-blue-to-white':
          'radial-gradient(187.48% 50% at 50% 50%, rgba(0, 174, 239, 0.36) 0%, rgba(0, 77, 134, 0.60) 87.5%), ${theme`colors.white`}',
        'button-gradient-gray':
          'linear-gradient(90deg, #FFFFFF 0%, #EDEFF3 10%, #EDEFF3 30%, #EDEFF3 70%, #EDEFF3 90%, #FFFFFF 100%)',
        'button-gradient-navy':
          'linear-gradient(90deg, rgba(0,77,134,1) 0%, rgba(0,77,134,0.8) 30%, rgba(0,77,134,0.8) 60%, rgba(0,77,134,1) 100%)',
        'gradient-navy-to-light-blue':
          'linear-gradient(110deg, rgba(0,77,134,1) 0%, rgba(0,77,134,1) 60%, rgba(0,174,239,1) 100%)'
      },
      letterSpacing: {
        ultra: '0.14em'
      },
      screens: {
        mobileSm: '480px',
        mobile: '640px',
        tablet: '770px',
        tabletLg: '900px',
        laptop: '1024px',
        laptopLg: '1200px',
        laptopXl: '1300px',
        desktop: '1440px',
        '1500': '1500px'
      },
      dropShadow: {
        'inverted-hover': '2px 2px 8px 0px rgba(0, 0, 0, 0.10)'
      },
      typography: ({ theme }) => ({
        DEFAULT: {
          css: {
            fontSize: '1.125rem',
            lineHeight: '1.5',
            '--tw-prose-body': theme('colors.black'),
            '--tw-prose-headings': theme('colors.navy'),
            '--tw-prose-lead': theme('colors.black'),
            '--tw-prose-links': theme('colors.navy'),
            '--tw-prose-bold': theme('colors.black'),
            '--tw-prose-counters': theme('colors.navy'),
            '--tw-prose-bullets': theme('colors.teal'),
            '--tw-prose-hr': theme('colors.navy'),
            '--tw-prose-quotes': theme('colors.black'),
            '--tw-prose-quote-borders': theme('colors.navy'),
            '--tw-prose-captions': theme('colors.black'),
            '--tw-prose-code': theme('colors.black'),
            '--tw-prose-pre-code': theme('colors.black'),
            '--tw-prose-pre-bg': theme('colors.black'),
            '--tw-prose-th-borders': theme('colors.black'),
            '--tw-prose-td-borders': theme('colors.black'),
            '--tw-prose-invert-body': theme('colors.white'),
            '--tw-prose-invert-headings': theme('colors.white'),
            '--tw-prose-invert-lead': theme('colors.white'),
            '--tw-prose-invert-links': theme('colors.white'),
            '--tw-prose-invert-bold': theme('colors.white'),
            '--tw-prose-invert-counters': theme('colors.white'),
            '--tw-prose-invert-bullets': theme('colors.white'),
            '--tw-prose-invert-hr': theme('colors.white'),
            '--tw-prose-invert-quotes': theme('colors.white'),
            '--tw-prose-invert-quote-borders': theme('colors.white'),
            '--tw-prose-invert-captions': theme('colors.white'),
            '--tw-prose-invert-code': theme('colors.white'),
            '--tw-prose-invert-pre-code': theme('colors.white'),
            '--tw-prose-invert-pre-bg': 'rgb(0 0 0 / 50%)',
            '--tw-prose-invert-th-borders': theme('colors.white'),
            '--tw-prose-invert-td-borders': theme('colors.white'),

            ':where(h1, h2, h3, h4, h5, h6, p)': {
              fontWeight: 400
            },
            ':where(h1, h2, h3, h4, h5, h6)': {
              marginBottom: '0.4em'
            },
            h2: {
              fontSize: '2.625em'
            },
            blockquote: {
              '& > p:first-of-type': {
                marginTop: 0
              },
              cite: {
                fontSize: '0.875rem'
              }
            }
          }
        }
      })
    }
  },
  plugins: [
    require('@tailwindcss/typography'),
    plugin(require('./tw-forms.config.js')),
    plugin(function ({ addUtilities, matchUtilities, addBase, theme }) {
      addBase({
        ':root': {
          '--tw-bite-size': '40px',
          '--tw-bite-x': '10px',
          '--tw-bite-y': '10px'
        }
      });

      addUtilities({
        '.bite': {
          '--tw-bite-mask':
            'radial-gradient(var(--tw-bite-size) at var(--tw-bite-x) var(--tw-bite-y), #0000 100%, #000 calc(100% + 1px))',
          maskImage: 'var(--tw-bite-mask)',
          maskRepeat: 'no-repeat'
        }
      });

      matchUtilities(
        {
          'bite-x': (value) => ({
            '--tw-bite-x': value
          }),
          'bite-y': (value) => ({
            '--tw-bite-y': value
          })
        },
        {
          values: theme('translate'),
          supportsNegativeValues: true
        }
      );

      matchUtilities(
        {
          bite: (value) => ({
            '--tw-bite-size': value
          })
        },
        {
          values: theme('size')
        }
      );
    })
    // plugin(function ({ addVariant }) {
    //   addVariant('menu', [
    //     '& .wp-block-navigation__container',
    //     '& .wp-block-navigation > ul'
    //   ]);
    //   addVariant('submenu', ['& .wp-block-navigation-submenu']);
    //   addVariant('submenu', ['& .wp-block-navigation-submenu']);
    //   addVariant('menu-item', '& li.wp-block-navigation-link');
    //   addVariant('menu-link', [
    //     '& .wp-block-navigation-link > a',
    //     '& .wp-block-navigation-item__content > div'
    //   ]);
    // })
  ]
};

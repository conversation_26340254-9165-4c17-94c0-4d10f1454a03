import { useEffect, useState } from '@wordpress/element';

export default function useEmbedUrl(url, type) {
  const [embedUrl, setEmbedUrl]: any = useState(false);
  const [videoId, setVideoId]: any = useState(null);

  useEffect(() => {
    if (!url) {
      setEmbedUrl(false);
      setVideoId(null);
      return;
    }

    if (type === 'youtube') {
      const regExp =
        /^.*(youtu\.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
      const match = url.match(regExp);
      if (match?.[2]?.length === 11) {
        setVideoId(match[2]);
        setEmbedUrl(`https://www.youtube.com/embed/${match[2]}`);
      } else {
        setEmbedUrl(false);
        setVideoId(null);
      }
    } else if (type === 'vimeo') {
      const regExp = /(?:vimeo)\.com.*(?:videos|video|channels|)\/([\d]+)/i;
      const match = url.match(regExp);

      if (match?.[1]) {
        setEmbedUrl(`https://player.vimeo.com/video/${match[1]}`);
      } else {
        setEmbedUrl(false);
      }
    }
  }, [url, type]);

  return { embedUrl, videoId };
}

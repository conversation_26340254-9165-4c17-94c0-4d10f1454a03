import { __ } from '@wordpress/i18n';

import { useBlockProps, useInnerBlocksProps } from '@wordpress/block-editor';

import { _class } from '../components/_utils';
import { Content_Blocks } from '../components';

export default function Edit({ attributes, setAttributes }) {
  const { children, ...innerBlocksProps } = useInnerBlocksProps(
    useBlockProps({
      className: _class({
        default: 'container'
      })
    }),
    {
      allowedBlocks: [...Content_Blocks],
      template: [['core/paragraph']]
    }
  );
  return (
    <div {...innerBlocksProps}>
      <div className="prose max-w-5xl">{children}</div>
    </div>
  );
}

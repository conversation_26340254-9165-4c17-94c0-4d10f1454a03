{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "takt/split-text", "version": "0.1.0", "title": "Split Text", "category": "custom", "icon": "button", "description": "", "keywords": [], "supports": {"html": false, "spacing": {"padding": ["top", "bottom"]}, "color": {"text": false, "background": true}}, "attributes": {"eyebrow": {"type": "string", "default": ""}, "title": {"type": "string", "default": ""}, "description": {"type": "string", "default": ""}, "style": {"type": "object", "default": {"spacing": {"padding": {"top": "var:preset|spacing|small", "bottom": "var:preset|spacing|small"}}}}}, "example": {"attributes": {"eyebrow": "Eyebrow Text", "title": "Split Text"}, "innerBlocks": [{"name": "core/paragraph", "attributes": {"content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat."}}, {"name": "core/paragraph", "attributes": {"content": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat."}}]}, "textdomain": "takt", "editorScript": "file:./index.js"}